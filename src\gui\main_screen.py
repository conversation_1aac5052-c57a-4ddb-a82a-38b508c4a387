# src/gui/main_screen.py
"""
Main screen for DTT application navigation
"""

from kivy.uix.boxlayout import BoxLayout
from kivy.uix.gridlayout import GridLayout
from kivy.uix.label import Label
from kivy.uix.button import Button
from kivy.uix.textinput import TextInput
from kivy.uix.popup import Popup
from kivy.graphics import Color, Rectangle
import logging
from typing import Optional, Callable, Dict, Any

from data import database

logger = logging.getLogger(__name__)


class SubjectSelectionPopup(Popup):
    """Popup for selecting or creating a subject."""

    def __init__(self, on_subject_selected: Callable[[str], None], **kwargs):
        super().__init__(**kwargs)
        self.title = "Select or Create Subject"
        self.size_hint = (0.8, 0.6)
        self.on_subject_selected = on_subject_selected

        content = BoxLayout(orientation='vertical', spacing=10, padding=10)

        # Subject ID input
        input_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height='40dp')
        input_layout.add_widget(Label(text="Subject ID:", size_hint_x=0.3))
        self.subject_input = TextInput(
            hint_text="Enter subject ID (e.g., SUB001)",
            multiline=False
        )
        input_layout.add_widget(self.subject_input)
        content.add_widget(input_layout)

        # Subject name input
        name_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height='40dp')
        name_layout.add_widget(Label(text="Subject Name:", size_hint_x=0.3))
        self.name_input = TextInput(
            hint_text="Enter subject name (optional)",
            multiline=False
        )
        name_layout.add_widget(self.name_input)
        content.add_widget(name_layout)

        # Buttons
        button_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height='50dp', spacing=10)

        select_button = Button(text="Select/Create Subject", on_press=self.select_subject)
        button_layout.add_widget(select_button)

        cancel_button = Button(text="Cancel", on_press=self.dismiss)
        button_layout.add_widget(cancel_button)

        content.add_widget(button_layout)
        self.content = content

    def select_subject(self, instance):
        """Handle subject selection/creation."""
        subject_id = self.subject_input.text.strip()
        subject_name = self.name_input.text.strip()

        if not subject_id:
            return

        # Try to get existing subject or create new one
        existing_subject = database.get_subject(subject_id)
        if not existing_subject:
            # Create new subject
            success = database.add_subject(subject_id, subject_name or None)
            if not success:
                logger.error(f"Failed to create subject {subject_id}")
                return
            logger.info(f"Created new subject: {subject_id}")
        else:
            logger.info(f"Selected existing subject: {subject_id}")

        self.on_subject_selected(subject_id)
        self.dismiss()


class MainScreen(BoxLayout):
    """
    Main screen for DTT application.
    Provides navigation to different parts of the application.
    """

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.orientation = 'vertical'
        self.padding = 20
        self.spacing = 10

        # Current state
        self.current_subject_id: Optional[str] = None

        # Callbacks for navigation
        self.on_start_session: Optional[Callable[[str], None]] = None
        self.on_view_data: Optional[Callable] = None
        self.on_settings: Optional[Callable] = None
        self.on_run_analysis: Optional[Callable] = None

        self.setup_ui()
        logger.info("MainScreen initialized")

    def setup_ui(self):
        """Set up the main screen interface."""
        # Add background color
        with self.canvas.before:
            Color(0.95, 0.95, 0.95, 1)  # Light gray background
            self.rect = Rectangle(size=self.size, pos=self.pos)
        self.bind(size=self.update_rect, pos=self.update_rect)

        # Title
        title_label = Label(
            text="DTT Response Strength Analysis System",
            font_size='28sp',
            size_hint_y=None,
            height='60dp',
            bold=True
        )
        self.add_widget(title_label)

        # Current subject display
        self.subject_label = Label(
            text="No subject selected",
            font_size='18sp',
            size_hint_y=None,
            height='40dp'
        )
        self.add_widget(self.subject_label)

        # Main buttons grid
        button_grid = GridLayout(
            cols=2,
            spacing=15,
            size_hint_y=None,
            height='300dp'
        )

        # Session management buttons
        self.start_session_btn = Button(
            text="Start New Session",
            font_size='18sp',
            on_press=self.start_session_pressed
        )
        button_grid.add_widget(self.start_session_btn)

        select_subject_btn = Button(
            text="Select Subject",
            font_size='18sp',
            on_press=self.select_subject_pressed
        )
        button_grid.add_widget(select_subject_btn)

        # Data and analysis buttons
        view_data_btn = Button(
            text="View Data",
            font_size='18sp',
            on_press=self.view_data_pressed
        )
        button_grid.add_widget(view_data_btn)

        run_analysis_btn = Button(
            text="Run Analysis",
            font_size='18sp',
            on_press=self.run_analysis_pressed
        )
        button_grid.add_widget(run_analysis_btn)

        # Settings and utilities
        settings_btn = Button(
            text="Settings",
            font_size='18sp',
            on_press=self.settings_pressed
        )
        button_grid.add_widget(settings_btn)

        test_r_btn = Button(
            text="Test R Connection",
            font_size='18sp',
            on_press=self.test_r_pressed
        )
        button_grid.add_widget(test_r_btn)

        self.add_widget(button_grid)

        # Status area
        self.status_label = Label(
            text="Ready",
            font_size='16sp',
            size_hint_y=None,
            height='40dp'
        )
        self.add_widget(self.status_label)

        # Update button states
        self.update_button_states()

    def update_rect(self, instance, value):
        """Update background rectangle."""
        self.rect.pos = instance.pos
        self.rect.size = instance.size

    def update_button_states(self):
        """Update button enabled/disabled states based on current state."""
        has_subject = self.current_subject_id is not None
        self.start_session_btn.disabled = not has_subject

        if has_subject:
            self.subject_label.text = f"Current Subject: {self.current_subject_id}"
        else:
            self.subject_label.text = "No subject selected"

    def start_session_pressed(self, instance):
        """Handle start session button press."""
        if not self.current_subject_id:
            self.status_label.text = "Please select a subject first"
            return

        if self.on_start_session:
            self.on_start_session(self.current_subject_id)
        else:
            logger.warning("No start session callback set")

    def select_subject_pressed(self, instance):
        """Handle select subject button press."""
        popup = SubjectSelectionPopup(on_subject_selected=self.set_current_subject)
        popup.open()

    def set_current_subject(self, subject_id: str):
        """Set the current subject."""
        self.current_subject_id = subject_id
        self.update_button_states()
        self.status_label.text = f"Selected subject: {subject_id}"
        logger.info(f"Set current subject: {subject_id}")

    def view_data_pressed(self, instance):
        """Handle view data button press."""
        if self.on_view_data:
            self.on_view_data()
        else:
            self.status_label.text = "Data viewing not implemented yet"

    def run_analysis_pressed(self, instance):
        """Handle run analysis button press."""
        if self.on_run_analysis:
            self.on_run_analysis()
        else:
            self.status_label.text = "Analysis not implemented yet"

    def settings_pressed(self, instance):
        """Handle settings button press."""
        if self.on_settings:
            self.on_settings()
        else:
            self.status_label.text = "Settings not implemented yet"

    def test_r_pressed(self, instance):
        """Handle test R connection button press."""
        self.status_label.text = "Testing R connection..."

        # Import here to avoid circular imports
        from ..main import trigger_r_analysis

        try:
            # Test R connection with a simple call
            trigger_r_analysis()
            self.status_label.text = "R connection test successful"
        except Exception as e:
            self.status_label.text = f"R connection test failed: {str(e)}"
            logger.error(f"R connection test failed: {e}")

    def set_status(self, message: str):
        """Set status message."""
        self.status_label.text = message