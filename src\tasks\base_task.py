# src/tasks/base_task.py
"""
Abstract base class for DTT tasks
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Callable
import logging
from dataclasses import dataclass
from enum import Enum

from utils.timing import TimingManager
from utils.audio import AudioManager

logger = logging.getLogger(__name__)


class TrialResult(Enum):
    """Possible trial outcomes."""
    CORRECT = "correct"
    INCORRECT = "incorrect"
    NO_RESPONSE = "no_response"
    TIMEOUT = "timeout"


@dataclass
class TrialData:
    """Data structure for trial information."""
    trial_number: int
    task_type: str
    target_skill: str
    stimulus_data: Dict[str, Any]
    response_data: Optional[Dict[str, Any]] = None
    result: Optional[TrialResult] = None
    timing_data: Optional[Dict[str, Any]] = None
    notes: str = ""


class BaseTask(ABC):
    """
    Abstract base class for all DTT tasks.

    Provides common functionality for:
    - Trial management
    - Timing
    - Audio feedback
    - Data collection
    """

    def __init__(self, task_name: str, target_skill: str):
        """
        Initialize base task.

        Args:
            task_name: Name of the task
            target_skill: Skill being targeted
        """
        self.task_name = task_name
        self.target_skill = target_skill
        self.timing_manager = TimingManager()
        self.audio_manager = AudioManager()

        # Trial management
        self.current_trial = 0
        self.trial_data_list: List[TrialData] = []
        self.session_active = False

        # Callbacks
        self.on_trial_complete: Optional[Callable[[TrialData], None]] = None
        self.on_session_complete: Optional[Callable[[List[TrialData]], None]] = None

        logger.info(f"Initialized {task_name} task for skill: {target_skill}")

    @abstractmethod
    def generate_stimulus(self, trial_number: int) -> Dict[str, Any]:
        """
        Generate stimulus data for a trial.

        Args:
            trial_number: Current trial number

        Returns:
            dict: Stimulus data for the trial
        """
        pass

    @abstractmethod
    def present_stimulus(self, stimulus_data: Dict[str, Any]) -> float:
        """
        Present the stimulus to the participant.

        Args:
            stimulus_data: Data describing the stimulus

        Returns:
            float: Timestamp when stimulus was presented
        """
        pass

    @abstractmethod
    def evaluate_response(self, response_data: Dict[str, Any],
                         stimulus_data: Dict[str, Any]) -> TrialResult:
        """
        Evaluate whether the response was correct.

        Args:
            response_data: Data about the participant's response
            stimulus_data: Data about the presented stimulus

        Returns:
            TrialResult: Result of the trial
        """
        pass

    @abstractmethod
    def cleanup_trial(self):
        """Clean up after a trial (remove stimuli, reset state, etc.)."""
        pass

    def start_session(self):
        """Start a new session."""
        self.session_active = True
        self.current_trial = 0
        self.trial_data_list = []
        self.timing_manager.start_session()
        self.audio_manager.play_session_start()
        logger.info(f"Started {self.task_name} session")

    def start_trial(self) -> TrialData:
        """
        Start a new trial.

        Returns:
            TrialData: Trial data object for the new trial
        """
        if not self.session_active:
            raise RuntimeError("Cannot start trial: session not active")

        self.current_trial += 1
        self.timing_manager.start_trial()

        # Generate stimulus for this trial
        stimulus_data = self.generate_stimulus(self.current_trial)

        # Create trial data object
        trial_data = TrialData(
            trial_number=self.current_trial,
            task_type=self.task_name,
            target_skill=self.target_skill,
            stimulus_data=stimulus_data
        )

        logger.debug(f"Started trial {self.current_trial}")
        return trial_data

    def present_trial_stimulus(self, trial_data: TrialData) -> float:
        """
        Present the stimulus for a trial.

        Args:
            trial_data: Trial data containing stimulus information

        Returns:
            float: Timestamp when stimulus was presented
        """
        sd_time = self.present_stimulus(trial_data.stimulus_data)
        self.timing_manager.mark_event('sd_presentation')
        return sd_time

    def record_response(self, response_data: Dict[str, Any],
                       trial_data: TrialData) -> TrialResult:
        """
        Record and evaluate a response.

        Args:
            response_data: Data about the response
            trial_data: Current trial data

        Returns:
            TrialResult: Result of the trial
        """
        # Mark response timing
        if 'first_response' not in self.timing_manager.trial_events:
            self.timing_manager.mark_event('first_response')

        # Add response timestamp to response data
        response_data['timestamp'] = self.timing_manager.trial_events.get('first_response')

        # Evaluate response
        result = self.evaluate_response(response_data, trial_data.stimulus_data)

        # Update trial data
        trial_data.response_data = response_data
        trial_data.result = result

        logger.debug(f"Recorded response for trial {trial_data.trial_number}: {result}")
        return result

    def complete_trial(self, trial_data: TrialData):
        """
        Complete the current trial.

        Args:
            trial_data: Trial data to complete
        """
        # End trial timing
        self.timing_manager.end_trial()
        trial_data.timing_data = self.timing_manager.get_trial_summary()

        # Provide feedback
        if trial_data.result == TrialResult.CORRECT:
            self.audio_manager.play_correct_feedback()
        elif trial_data.result == TrialResult.INCORRECT:
            self.audio_manager.play_incorrect_feedback()

        # Clean up trial
        self.cleanup_trial()

        # Store trial data
        self.trial_data_list.append(trial_data)

        # Reset timing for next trial
        self.timing_manager.reset_trial()

        # Call completion callback
        if self.on_trial_complete:
            self.on_trial_complete(trial_data)

        logger.info(f"Completed trial {trial_data.trial_number}: {trial_data.result}")

    def end_session(self):
        """End the current session."""
        if not self.session_active:
            return

        self.session_active = False
        self.audio_manager.play_session_end()

        # Call session completion callback
        if self.on_session_complete:
            self.on_session_complete(self.trial_data_list)

        logger.info(f"Ended {self.task_name} session with {len(self.trial_data_list)} trials")

    def get_session_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the current session.

        Returns:
            dict: Session summary data
        """
        if not self.trial_data_list:
            return {}

        correct_trials = sum(1 for trial in self.trial_data_list
                           if trial.result == TrialResult.CORRECT)
        total_trials = len(self.trial_data_list)

        return {
            'task_name': self.task_name,
            'target_skill': self.target_skill,
            'total_trials': total_trials,
            'correct_trials': correct_trials,
            'accuracy': correct_trials / total_trials if total_trials > 0 else 0,
            'session_active': self.session_active
        }
