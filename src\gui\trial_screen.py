# src/gui/trial_screen.py
"""
Trial screen for DTT task presentation
"""

from kivy.uix.floatlayout import FloatLayout
from kivy.uix.label import Label
from kivy.uix.button import Button
from kivy.uix.widget import Widget
from kivy.uix.image import Image
from kivy.graphics import Color, Rectangle, Ellipse
from kivy.clock import Clock
from kivy.properties import ObjectProperty, StringProperty, NumericProperty
from kivy.vector import Vector
import logging
from typing import Optional, Callable, Dict, Any
from pathlib import Path

from tasks.base_task import BaseTask, TrialData, TrialResult
from tasks.clothes_sorting import ClothesSortingTask

logger = logging.getLogger(__name__)


class ShirtStimulusWidget(Image):
    """Widget for displaying shirt stimuli with drag and drop capability."""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.stimulus_data = None
        self.dragging = False
        self.drag_start_pos = None
        self.original_pos = None

    def set_stimulus(self, stimulus_data: Dict[str, Any]):
        """Set the stimulus data and update display."""
        self.stimulus_data = stimulus_data

        # Load shirt image
        if "shirt_image_path" in stimulus_data:
            image_path = stimulus_data["shirt_image_path"]
            if Path(image_path).exists():
                self.source = image_path
                logger.debug(f"Loaded shirt image: {image_path}")
            else:
                logger.warning(f"Shirt image not found: {image_path}")
                # Use a placeholder or default image
                self.source = ""

        # Set position and size
        if "stimulus_position" in stimulus_data:
            pos_x, pos_y = stimulus_data["stimulus_position"]
            self.pos_hint = {'center_x': pos_x, 'center_y': pos_y}

        # Set size
        if "size" in stimulus_data:
            self.size_hint = stimulus_data["size"]
        else:
            self.size_hint = (0.15, 0.2)  # Default size

    def on_touch_down(self, touch):
        """Handle touch down for drag start."""
        if self.collide_point(*touch.pos):
            self.dragging = True
            self.drag_start_pos = touch.pos
            self.original_pos = self.pos
            return True
        return super().on_touch_down(touch)

    def on_touch_move(self, touch):
        """Handle touch move for dragging."""
        if self.dragging and touch.grab_current is None:
            # Update position during drag
            dx = touch.pos[0] - self.drag_start_pos[0]
            dy = touch.pos[1] - self.drag_start_pos[1]
            self.pos = (self.original_pos[0] + dx, self.original_pos[1] + dy)
            return True
        return super().on_touch_move(touch)

    def on_touch_up(self, touch):
        """Handle touch up for drag end."""
        if self.dragging:
            self.dragging = False
            # Return to original position (will be handled by parent)
            self.pos = self.original_pos
            return True
        return super().on_touch_up(touch)


class LaundryBasketWidget(Image):
    """Widget representing a laundry basket."""

    basket_category = StringProperty("")

    def __init__(self, basket_category: str, image_path: str, **kwargs):
        super().__init__(**kwargs)
        self.basket_category = basket_category

        # Load basket image
        if Path(image_path).exists():
            self.source = image_path
            logger.debug(f"Loaded basket image: {image_path}")
        else:
            logger.warning(f"Basket image not found: {image_path}")
            self.source = ""

    def set_basket_data(self, basket_data: Dict[str, Any]):
        """Set basket data and update display."""
        if "image_path" in basket_data:
            image_path = basket_data["image_path"]
            if Path(image_path).exists():
                self.source = image_path
            else:
                logger.warning(f"Basket image not found: {image_path}")

        if "position" in basket_data:
            pos_x, pos_y = basket_data["position"]
            self.pos_hint = {'center_x': pos_x, 'center_y': pos_y}

        if "size" in basket_data:
            self.size_hint = basket_data["size"]


class TrialScreen(FloatLayout):
    """
    Screen for conducting DTT trials.
    Handles stimulus presentation, response collection, and feedback.
    """

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        # Task management
        self.current_task: Optional[BaseTask] = None
        self.current_trial_data: Optional[TrialData] = None
        self.trial_in_progress = False

        # Callbacks
        self.on_trial_complete: Optional[Callable[[TrialData], None]] = None
        self.on_session_complete: Optional[Callable] = None

        # UI elements
        self.stimulus_widget = None
        self.bin_widgets = {}
        self.instruction_label = None
        self.feedback_label = None
        self.control_buttons = {}

        self.setup_ui()
        logger.info("TrialScreen initialized")

    def setup_ui(self):
        """Set up the user interface elements."""
        # Instruction label at top
        self.instruction_label = Label(
            text="Touch the correct color bin for the item",
            size_hint=(1, 0.1),
            pos_hint={'x': 0, 'top': 1},
            font_size='18sp'
        )
        self.add_widget(self.instruction_label)

        # Feedback label (initially hidden)
        self.feedback_label = Label(
            text="",
            size_hint=(0.5, 0.1),
            pos_hint={'center_x': 0.5, 'y': 0.1},
            font_size='24sp'
        )
        self.add_widget(self.feedback_label)

        # Stimulus area (center)
        self.stimulus_widget = ShirtStimulusWidget(
            size_hint=(0.15, 0.2),
            pos_hint={'center_x': 0.5, 'center_y': 0.3}
        )
        self.add_widget(self.stimulus_widget)

        # Control buttons
        self.control_buttons['start_trial'] = Button(
            text="Start Trial",
            size_hint=(0.2, 0.08),
            pos_hint={'x': 0.02, 'y': 0.02},
            on_press=self.start_trial_pressed
        )
        self.add_widget(self.control_buttons['start_trial'])

        self.control_buttons['end_session'] = Button(
            text="End Session",
            size_hint=(0.2, 0.08),
            pos_hint={'x': 0.78, 'y': 0.02},
            on_press=self.end_session_pressed
        )
        self.add_widget(self.control_buttons['end_session'])

    def set_task(self, task: BaseTask):
        """Set the current task."""
        self.current_task = task
        self.current_task.on_trial_complete = self.handle_trial_complete
        self.current_task.on_session_complete = self.handle_session_complete

        # Update instruction text
        if hasattr(task, 'get_task_instructions'):
            self.instruction_label.text = task.get_task_instructions()

        # Set up laundry baskets for clothes sorting task
        if isinstance(task, ClothesSortingTask):
            self.setup_laundry_baskets(task)

        logger.info(f"Set task: {task.task_name}")

    def setup_laundry_baskets(self, task: ClothesSortingTask):
        """Set up laundry baskets for clothes sorting task."""
        # Clear existing baskets
        for basket_widget in self.bin_widgets.values():
            self.remove_widget(basket_widget)
        self.bin_widgets = {}

        # Get current basket positions from task
        basket_positions = task.current_basket_positions

        # Create new basket widgets
        for basket_category, basket_data in basket_positions.items():
            basket_widget = LaundryBasketWidget(
                basket_category=basket_category,
                image_path=basket_data['image_path'],
                size_hint=basket_data['size'],
                pos_hint={
                    'center_x': basket_data['position'][0],
                    'center_y': basket_data['position'][1]
                }
            )
            self.bin_widgets[basket_category] = basket_widget
            self.add_widget(basket_widget)

        logger.debug(f"Set up {len(self.bin_widgets)} laundry baskets")

    def start_trial_pressed(self, instance):
        """Handle start trial button press."""
        if not self.current_task:
            logger.warning("No task set")
            return

        if self.trial_in_progress:
            logger.warning("Trial already in progress")
            return

        self.start_trial()

    def start_trial(self):
        """Start a new trial."""
        if not self.current_task:
            return

        try:
            # Start trial in task
            self.current_trial_data = self.current_task.start_trial()
            self.trial_in_progress = True

            # Present stimulus
            sd_time = self.current_task.present_trial_stimulus(self.current_trial_data)

            # Update stimulus display
            self.stimulus_widget.set_stimulus(self.current_trial_data.stimulus_data)

            # Clear feedback
            self.feedback_label.text = ""

            # Disable start button
            self.control_buttons['start_trial'].disabled = True

            logger.info(f"Started trial {self.current_trial_data.trial_number}")

        except Exception as e:
            logger.error(f"Error starting trial: {e}")
            self.trial_in_progress = False

    def on_touch_down(self, touch):
        """Handle touch events for response collection."""
        # Let parent handle first
        if super().on_touch_down(touch):
            return True

        # Only handle touches during trials
        if not self.trial_in_progress or not self.current_task:
            return False

        # Convert touch position to normalized coordinates
        norm_x = touch.x / self.width
        norm_y = touch.y / self.height

        # Check if touch is on a laundry basket (for clothes sorting)
        if isinstance(self.current_task, ClothesSortingTask):
            response_data = self.current_task.handle_touch((norm_x, norm_y))

            if response_data.get('selected_basket'):
                # Record response
                result = self.current_task.record_response(response_data, self.current_trial_data)

                # Complete trial
                self.complete_trial()
                return True

        return False

    def complete_trial(self):
        """Complete the current trial."""
        if not self.trial_in_progress or not self.current_task:
            return

        try:
            # Complete trial in task
            self.current_task.complete_trial(self.current_trial_data)

            # Show feedback
            if self.current_trial_data.result == TrialResult.CORRECT:
                self.feedback_label.text = "Correct!"
                self.feedback_label.color = (0, 1, 0, 1)  # Green
            elif self.current_trial_data.result == TrialResult.INCORRECT:
                self.feedback_label.text = "Try again!"
                self.feedback_label.color = (1, 0, 0, 1)  # Red
            else:
                self.feedback_label.text = "No response"
                self.feedback_label.color = (1, 1, 0, 1)  # Yellow

            # Clear feedback after delay
            Clock.schedule_once(self.clear_feedback, 2.0)

            # Re-enable start button after delay
            Clock.schedule_once(self.enable_start_button, 2.0)

            self.trial_in_progress = False

            logger.info(f"Completed trial {self.current_trial_data.trial_number}: {self.current_trial_data.result}")

        except Exception as e:
            logger.error(f"Error completing trial: {e}")
            self.trial_in_progress = False

    def clear_feedback(self, dt):
        """Clear feedback text."""
        self.feedback_label.text = ""

    def enable_start_button(self, dt):
        """Re-enable start trial button."""
        self.control_buttons['start_trial'].disabled = False

    def handle_trial_complete(self, trial_data: TrialData):
        """Handle trial completion callback from task."""
        if self.on_trial_complete:
            self.on_trial_complete(trial_data)

    def handle_session_complete(self, trial_data_list):
        """Handle session completion callback from task."""
        if self.on_session_complete:
            self.on_session_complete()

    def end_session_pressed(self, instance):
        """Handle end session button press."""
        if self.current_task and self.current_task.session_active:
            self.current_task.end_session()
            self.feedback_label.text = "Session ended"
            self.feedback_label.color = (0, 0, 1, 1)  # Blue
            Clock.schedule_once(self.clear_feedback, 3.0)
