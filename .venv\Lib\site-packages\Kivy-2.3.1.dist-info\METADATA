Metadata-Version: 2.1
Name: Kivy
Version: 2.3.1
Summary: An open-source Python framework for developing GUI apps that work cross-platform, including desktop, mobile and embedded platforms.
Home-page: http://kivy.org
Author: Kivy Team and other contributors
Author-email: <EMAIL>
License: MIT
Project-URL: Source, https://github.com/kivy/kivy
Project-URL: Documentation, https://kivy.org/doc/stable/
Project-URL: Bug Reports, https://github.com/kivy/kivy/issues
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: MacOS X
Classifier: Environment :: Win32 (MS Windows)
Classifier: Environment :: X11 Applications
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: End Users/Desktop
Classifier: Intended Audience :: Information Technology
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: MIT License
Classifier: Natural Language :: English
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: POSIX :: BSD :: FreeBSD
Classifier: Operating System :: POSIX :: Linux
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Artistic Software
Classifier: Topic :: Games/Entertainment
Classifier: Topic :: Multimedia :: Graphics :: 3D Rendering
Classifier: Topic :: Multimedia :: Graphics :: Capture :: Digital Camera
Classifier: Topic :: Multimedia :: Graphics :: Presentation
Classifier: Topic :: Multimedia :: Graphics :: Viewers
Classifier: Topic :: Multimedia :: Sound/Audio :: Players :: MP3
Classifier: Topic :: Multimedia :: Video :: Display
Classifier: Topic :: Scientific/Engineering :: Human Machine Interfaces
Classifier: Topic :: Scientific/Engineering :: Visualization
Classifier: Topic :: Software Development :: Libraries :: Application Frameworks
Classifier: Topic :: Software Development :: User Interfaces
Requires-Python: >=3.8
Description-Content-Type: text/markdown
License-File: LICENSE
License-File: AUTHORS
Requires-Dist: Kivy-Garden>=0.1.4
Requires-Dist: docutils
Requires-Dist: pygments
Requires-Dist: requests
Requires-Dist: filetype
Requires-Dist: kivy-deps.angle~=0.4.0; sys_platform == "win32"
Requires-Dist: kivy-deps.sdl2~=0.8.0; sys_platform == "win32"
Requires-Dist: kivy-deps.glew~=0.3.1; sys_platform == "win32"
Requires-Dist: pypiwin32; sys_platform == "win32"
Provides-Extra: angle
Requires-Dist: kivy-deps.angle~=0.4.0; sys_platform == "win32" and extra == "angle"
Provides-Extra: base
Requires-Dist: pillow<11,>=9.5.0; extra == "base"
Provides-Extra: dev
Requires-Dist: pytest>=3.6; extra == "dev"
Requires-Dist: pytest-cov; extra == "dev"
Requires-Dist: pytest-asyncio!=0.11.0; extra == "dev"
Requires-Dist: pytest-timeout; extra == "dev"
Requires-Dist: pytest-benchmark; extra == "dev"
Requires-Dist: pyinstaller; extra == "dev"
Requires-Dist: sphinx~=6.2.1; extra == "dev"
Requires-Dist: sphinxcontrib-jquery~=4.1; extra == "dev"
Requires-Dist: flake8; extra == "dev"
Requires-Dist: pre-commit; extra == "dev"
Requires-Dist: responses; extra == "dev"
Requires-Dist: kivy-deps.gstreamer-dev~=0.3.3; sys_platform == "win32" and extra == "dev"
Requires-Dist: kivy-deps.sdl2-dev~=0.8.0; sys_platform == "win32" and extra == "dev"
Requires-Dist: kivy-deps.glew-dev~=0.3.1; sys_platform == "win32" and extra == "dev"
Provides-Extra: full
Requires-Dist: pillow<11,>=9.5.0; extra == "full"
Requires-Dist: ffpyplayer; (sys_platform == "linux" or sys_platform == "darwin") and extra == "full"
Requires-Dist: kivy-deps.gstreamer~=0.3.3; sys_platform == "win32" and extra == "full"
Provides-Extra: glew
Requires-Dist: kivy-deps.glew~=0.3.1; sys_platform == "win32" and extra == "glew"
Provides-Extra: gstreamer
Requires-Dist: kivy-deps.gstreamer~=0.3.3; sys_platform == "win32" and extra == "gstreamer"
Provides-Extra: media
Requires-Dist: ffpyplayer; (sys_platform == "linux" or sys_platform == "darwin") and extra == "media"
Requires-Dist: kivy-deps.gstreamer~=0.3.3; sys_platform == "win32" and extra == "media"
Provides-Extra: sdl2
Requires-Dist: kivy-deps.sdl2~=0.8.0; sys_platform == "win32" and extra == "sdl2"
Provides-Extra: tuio
Requires-Dist: oscpy; extra == "tuio"

Kivy

====



<img align="right" height="256" src="https://raw.githubusercontent.com/kivy/kivy/master/kivy/data/logo/kivy-icon-256.png"/>



[Kivy](https://www.kivy.org) is an open-source [Python](https://python.org) framework

for developing GUI apps that work cross-platform, including desktop, mobile and

embedded platforms.



The aim is to allow for quick and easy interaction design and rapid prototyping

whilst making your code reusable and deployable: Innovative user interfaces made

easy.



Kivy is written in Python and [Cython](https://cython.org/) and is built on

[OpenGL ES 2.0](https://www.khronos.org/opengles/). It supports various input 

devices and has an extensive (and extensible) widget library. With the

same codebase, you can target Windows, macOS, Linux (including Raspberry Pi OS),

Android, and iOS. All Kivy widgets are built with multitouch support.



Kivy is [MIT licensed](LICENSE), actively developed by a great community and is

supported by many projects managed by the 

[Kivy Organization](https://www.kivy.org/about.html).



[![Backers on Open Collective](https://opencollective.com/kivy/backers/badge.svg)](#backers)

[![Sponsors on Open Collective](https://opencollective.com/kivy/sponsors/badge.svg)](#sponsors)

[![Contributor Covenant](https://img.shields.io/badge/Contributor%20Covenant-2.1-4baaaa.svg)](CODE_OF_CONDUCT.md)

[![GitHub contributors](https://img.shields.io/github/contributors-anon/kivy/kivy)](https://github.com/kivy/kivy/graphs/contributors)



![PyPI - Version](https://img.shields.io/pypi/v/kivy)

![PyPI - Python Version](https://img.shields.io/pypi/pyversions/kivy)



[![Windows Unittests Status](https://github.com/kivy/kivy/workflows/Windows%20Unittests/badge.svg)](https://github.com/kivy/kivy/actions?query=workflow%3A%22Windows+Unittests%22)

[![Ubuntu Unittests Status](https://github.com/kivy/kivy/workflows/Ubuntu%20Unittests/badge.svg)](https://github.com/kivy/kivy/actions?query=workflow%3A%22Ubuntu+Unittests%22)

[![OSX Unittests Status](https://github.com/kivy/kivy/workflows/OSX%20Unittests/badge.svg)](https://github.com/kivy/kivy/actions?query=workflow%3A%22OSX+Unittests%22)

[![Coverage Status](https://coveralls.io/repos/kivy/kivy/badge.svg?branch=master)](https://coveralls.io/r/kivy/kivy?branch=master)



[![Windows wheels Status](https://github.com/kivy/kivy/workflows/Windows%20wheels/badge.svg)](https://github.com/kivy/kivy/actions?query=workflow%3A%22Windows+wheels%22)

[![Manylinux wheels Status](https://github.com/kivy/kivy/workflows/Manylinux%20wheels/badge.svg)](https://github.com/kivy/kivy/actions?query=workflow%3A%22Manylinux+wheels%22)

[![Raspberry Pi wheels Status](https://github.com/kivy/kivy/workflows/RPi%20wheels/badge.svg)](https://github.com/kivy/kivy/actions?query=workflow%3A%22RPi+wheels%22)

[![OSX wheels Status](https://github.com/kivy/kivy/workflows/OSX%20wheels%2Fapp/badge.svg)](https://github.com/kivy/kivy/actions?query=workflow%3A%22OSX+wheels%2Fapp%22)



Installation, Documentation and Examples

----------------------------------------



Extensive installation instructions as well as tutorials and general

documentation, including an API reference, can be found at https://www.kivy.org/docs.

A [PDF version](https://media.readthedocs.org/pdf/kivy/latest/kivy.pdf) is also available.



Kivy ships with many examples which can be found in the `examples` folder.



Support

-------



Are you having trouble using the Kivy framework, or any of its related projects?

Is there an error you don’t understand? Are you trying to figure out how to use 

it? We have volunteers who can help!



The best channels to contact us for support are listed in the latest 

[Contact Us](CONTACT.md) document.



Contributing

------------



We love pull requests and discussing novel ideas. Check out our

[latest contribution guide](CONTRIBUTING.md) and

feel free to improve Kivy.



It gives details of the best places online to discuss the development with the

core developers and other enthusiasts.



Sibling projects

----------------



The Kivy team manager a number of additional projects that support the Kivy 

eco-system.



- [Buildozer](https://github.com/kivy/buildozer):  a development tool for turning Python applications into binary packages ready for installation on

  any of a number of platforms, including mobile devices.

- [Plyer](https://github.com/kivy/plyer): a platform-independent Python API for accessing hardware features of various platforms (Android, iOS,

  macOS, Linux and Windows).

- [PyJNIus](https://github.com/kivy/pyjnius): a Python library for accessing Java classes using the Java Native Interface (JNI).

- [Pyobjus](https://github.com/kivy/pyobjus): Python module for accessing Objective-C classes as Python classes using 

  Objective-C runtime reflection.

- [Python for Android](https://github.com/kivy/python-for-android): a development tool that packages Python apps into binaries that can run on Android devices.

- [Kivy iOS](https://github.com/kivy/kivy-ios): a toolchain to compile the necessary libraries for iOS to run Kivy applications, and manage the

  creation of Xcode projects.

- [Audiostream](https://github.com/kivy/audiostream): library for direct access

  to the microphone and speaker.

- [KivEnt](https://github.com/kivy/kivent): entity-based game engine for Kivy.

- [Oscpy](https://github.com/kivy/oscpy/): a Python implementation of Open Sound Control (OSC) network protocol.

- [Garden](https://github.com/kivy-garden): widgets and libraries created and

  maintained by users.



Licenses

--------



- Kivy is released under the terms of the MIT License. Please refer to the

  [LICENSE](LICENSE) file.

- The provided fonts Roboto and Roboto Mono are licensed and

  distributed under the terms of the

  [Apache License, Version 2.0](https://www.apache.org/licenses/LICENSE-2.0).

  The DejaVuSans (used for the virtual keyboard) license can be viewed

  [here](https://github.com/dejavu-fonts/dejavu-fonts/blob/master/LICENSE).

- The current UI design has been adapted from Moblintouch theme's SVGs

  and is licensed under the terms of the

  [LGPLv2.1](https://www.gnu.org/licenses/old-licenses/lgpl-2.1).





## Code of Conduct



In the interest of fostering an open and welcoming community, we as 

contributors and maintainers need to ensure participation in our project and 

our sister projects is a harassment-free and positive experience for everyone. 

It is vital that all interaction is conducted in a manner conveying respect, 

open-mindedness and gratitude.



Please consult the [latest Code of Conduct](https://github.com/kivy/kivy/blob/master/CODE_OF_CONDUCT.md).



## Contributors



This project exists thanks to 

[all the people who contribute](https://github.com/kivy/kivy/graphs/contributors).

[[Become a contributor](CONTRIBUTING.md)].



<img src="https://contrib.nn.ci/api?repo=kivy/kivy&pages=5&no_bot=true&radius=22&cols=18">



## Backers



Thank you to [all of our backers](https://opencollective.com/kivy)! 

🙏 [[Become a backer](https://opencollective.com/kivy#backer)]



<img src="https://opencollective.com/kivy/backers.svg?width=890&avatarHeight=44&button=false">



## Sponsors



Special thanks to 

[all of our sponsors, past and present](https://opencollective.com/kivy).

Support this project by 

[[becoming a sponsor](https://opencollective.com/kivy#sponsor)].



Here are our top current sponsors. Please click through to see their websites,

and support them as they support us. 



<!--- See https://github.com/orgs/kivy/discussions/15 for explanation of this code. -->

<a href="https://opencollective.com/kivy/sponsor/0/website" target="_blank"><img src="https://opencollective.com/kivy/sponsor/0/avatar.svg"></a>

<a href="https://opencollective.com/kivy/sponsor/1/website" target="_blank"><img src="https://opencollective.com/kivy/sponsor/1/avatar.svg"></a>

<a href="https://opencollective.com/kivy/sponsor/2/website" target="_blank"><img src="https://opencollective.com/kivy/sponsor/2/avatar.svg"></a>

<a href="https://opencollective.com/kivy/sponsor/3/website" target="_blank"><img src="https://opencollective.com/kivy/sponsor/3/avatar.svg"></a>



<a href="https://opencollective.com/kivy/sponsor/4/website" target="_blank"><img src="https://opencollective.com/kivy/sponsor/4/avatar.svg"></a>

<a href="https://opencollective.com/kivy/sponsor/5/website" target="_blank"><img src="https://opencollective.com/kivy/sponsor/5/avatar.svg"></a>

<a href="https://opencollective.com/kivy/sponsor/6/website" target="_blank"><img src="https://opencollective.com/kivy/sponsor/6/avatar.svg"></a>

<a href="https://opencollective.com/kivy/sponsor/7/website" target="_blank"><img src="https://opencollective.com/kivy/sponsor/7/avatar.svg"></a>



<a href="https://opencollective.com/kivy/sponsor/8/website" target="_blank"><img src="https://opencollective.com/kivy/sponsor/8/avatar.svg"></a>

<a href="https://opencollective.com/kivy/sponsor/9/website" target="_blank"><img src="https://opencollective.com/kivy/sponsor/9/avatar.svg"></a>

<a href="https://opencollective.com/kivy/sponsor/10/website" target="_blank"><img src="https://opencollective.com/kivy/sponsor/10/avatar.svg"></a>

<a href="https://opencollective.com/kivy/sponsor/11/website" target="_blank"><img src="https://opencollective.com/kivy/sponsor/11/avatar.svg"></a>



<a href="https://opencollective.com/kivy/sponsor/12/website" target="_blank"><img src="https://opencollective.com/kivy/sponsor/12/avatar.svg"></a>

<a href="https://opencollective.com/kivy/sponsor/13/website" target="_blank"><img src="https://opencollective.com/kivy/sponsor/13/avatar.svg"></a>

<a href="https://opencollective.com/kivy/sponsor/14/website" target="_blank"><img src="https://opencollective.com/kivy/sponsor/14/avatar.svg"></a>

<a href="https://opencollective.com/kivy/sponsor/15/website" target="_blank"><img src="https://opencollective.com/kivy/sponsor/15/avatar.svg"></a>

