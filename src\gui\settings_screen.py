# src/gui/settings_screen.py
"""
Settings screen for DTT application configuration
"""

from kivy.uix.boxlayout import BoxLayout
from kivy.uix.gridlayout import GridLayout
from kivy.uix.label import Label
from kivy.uix.button import Button
from kivy.uix.textinput import TextInput
from kivy.uix.spinner import Spinner
from kivy.uix.switch import Switch
from kivy.uix.slider import Slider
from kivy.uix.scrollview import ScrollView
import logging
from typing import Dict, Any, Callable, Optional

logger = logging.getLogger(__name__)


class SettingsScreen(BoxLayout):
    """
    Settings screen for configuring DTT application parameters.
    """

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.orientation = 'vertical'

        # Settings data
        self.settings = {
            'subject_id': '',
            'session_prefix': 'SESS',
            'task_type': 'clothes_sorting',
            'difficulty_level': 2,
            'positioning_mode': 'randomized_fixed',
            'audio_enabled': True,
            'audio_volume': 0.8,
            'trial_timeout': 30.0,
            'inter_trial_interval': 2.0,
            'feedback_duration': 2.0,
            'auto_advance': True,
            'data_export_format': 'csv'
        }

        # Callbacks
        self.on_settings_changed: Optional[Callable[[Dict[str, Any]], None]] = None
        self.on_back_pressed: Optional[Callable] = None

        self.setup_ui()
        logger.info("SettingsScreen initialized")

    def setup_ui(self):
        """Set up the settings interface."""
        # Title
        title_label = Label(
            text="DTT Settings",
            size_hint_y=None,
            height='40dp',
            font_size='24sp'
        )
        self.add_widget(title_label)

        # Scrollable settings area
        scroll = ScrollView()
        settings_layout = GridLayout(
            cols=2,
            spacing=10,
            size_hint_y=None,
            padding=20
        )
        settings_layout.bind(minimum_height=settings_layout.setter('height'))

        # Subject Settings
        self.add_setting_section(settings_layout, "Subject Settings")

        self.add_text_setting(
            settings_layout,
            "Subject ID:",
            'subject_id',
            "Enter subject identifier"
        )

        self.add_text_setting(
            settings_layout,
            "Session Prefix:",
            'session_prefix',
            "Prefix for session IDs"
        )

        # Task Settings
        self.add_setting_section(settings_layout, "Task Settings")

        self.add_spinner_setting(
            settings_layout,
            "Task Type:",
            'task_type',
            ['clothes_sorting', 'color_matching', 'shape_sorting']
        )

        self.add_spinner_setting(
            settings_layout,
            "Difficulty Level:",
            'difficulty_level',
            ['1 (Easy)', '2 (Medium)', '3 (Hard)']
        )

        self.add_spinner_setting(
            settings_layout,
            "Basket Positioning:",
            'positioning_mode',
            ['fixed', 'randomized_fixed', 'random']
        )

        # Audio Settings
        self.add_setting_section(settings_layout, "Audio Settings")

        self.add_switch_setting(
            settings_layout,
            "Audio Enabled:",
            'audio_enabled'
        )

        self.add_slider_setting(
            settings_layout,
            "Audio Volume:",
            'audio_volume',
            min_val=0.0,
            max_val=1.0,
            step=0.1
        )

        # Timing Settings
        self.add_setting_section(settings_layout, "Timing Settings")

        self.add_slider_setting(
            settings_layout,
            "Trial Timeout (s):",
            'trial_timeout',
            min_val=5.0,
            max_val=60.0,
            step=5.0
        )

        self.add_slider_setting(
            settings_layout,
            "Inter-trial Interval (s):",
            'inter_trial_interval',
            min_val=0.5,
            max_val=10.0,
            step=0.5
        )

        self.add_slider_setting(
            settings_layout,
            "Feedback Duration (s):",
            'feedback_duration',
            min_val=1.0,
            max_val=5.0,
            step=0.5
        )

        # Behavior Settings
        self.add_setting_section(settings_layout, "Behavior Settings")

        self.add_switch_setting(
            settings_layout,
            "Auto Advance Trials:",
            'auto_advance'
        )

        # Data Settings
        self.add_setting_section(settings_layout, "Data Settings")

        self.add_spinner_setting(
            settings_layout,
            "Export Format:",
            'data_export_format',
            ['csv', 'json', 'xlsx']
        )

        scroll.add_widget(settings_layout)
        self.add_widget(scroll)

        # Control buttons
        button_layout = BoxLayout(
            size_hint_y=None,
            height='50dp',
            spacing=10,
            padding=20
        )

        save_button = Button(
            text="Save Settings",
            on_press=self.save_settings
        )
        button_layout.add_widget(save_button)

        reset_button = Button(
            text="Reset to Defaults",
            on_press=self.reset_settings
        )
        button_layout.add_widget(reset_button)

        back_button = Button(
            text="Back",
            on_press=self.back_pressed
        )
        button_layout.add_widget(back_button)

        self.add_widget(button_layout)

    def add_setting_section(self, layout: GridLayout, title: str):
        """Add a section header to the settings."""
        # Section title spans both columns
        section_label = Label(
            text=title,
            size_hint_y=None,
            height='30dp',
            font_size='18sp',
            bold=True
        )
        layout.add_widget(section_label)
        layout.add_widget(Label())  # Empty cell for grid alignment

    def add_text_setting(self, layout: GridLayout, label_text: str,
                         setting_key: str, hint_text: str = ""):
        """Add a text input setting."""
        label = Label(
            text=label_text,
            size_hint_y=None,
            height='40dp',
            halign='left'
        )
        label.bind(size=label.setter('text_size'))
        layout.add_widget(label)

        text_input = TextInput(
            text=str(self.settings[setting_key]),
            hint_text=hint_text,
            size_hint_y=None,
            height='40dp',
            multiline=False
        )
        text_input.bind(text=lambda instance, value, key=setting_key: self.update_setting(key, value))
        layout.add_widget(text_input)

    def add_spinner_setting(self, layout: GridLayout, label_text: str,
                           setting_key: str, values: list):
        """Add a spinner (dropdown) setting."""
        label = Label(
            text=label_text,
            size_hint_y=None,
            height='40dp',
            halign='left'
        )
        label.bind(size=label.setter('text_size'))
        layout.add_widget(label)

        # Handle difficulty level special case
        if setting_key == 'difficulty_level':
            current_value = f"{self.settings[setting_key]} ({'Easy' if self.settings[setting_key] == 1 else 'Medium' if self.settings[setting_key] == 2 else 'Hard'})"
        else:
            current_value = str(self.settings[setting_key])

        spinner = Spinner(
            text=current_value,
            values=values,
            size_hint_y=None,
            height='40dp'
        )

        def on_spinner_select(instance, text, key=setting_key):
            if key == 'difficulty_level':
                # Extract number from difficulty level text
                value = int(text.split()[0])
            else:
                value = text
            self.update_setting(key, value)

        spinner.bind(text=on_spinner_select)
        layout.add_widget(spinner)

    def add_switch_setting(self, layout: GridLayout, label_text: str, setting_key: str):
        """Add a switch (boolean) setting."""
        label = Label(
            text=label_text,
            size_hint_y=None,
            height='40dp',
            halign='left'
        )
        label.bind(size=label.setter('text_size'))
        layout.add_widget(label)

        switch = Switch(
            active=self.settings[setting_key],
            size_hint_y=None,
            height='40dp'
        )
        switch.bind(active=lambda instance, value, key=setting_key: self.update_setting(key, value))
        layout.add_widget(switch)

    def add_slider_setting(self, layout: GridLayout, label_text: str,
                          setting_key: str, min_val: float, max_val: float, step: float):
        """Add a slider setting."""
        label = Label(
            text=f"{label_text} {self.settings[setting_key]:.1f}",
            size_hint_y=None,
            height='40dp',
            halign='left'
        )
        label.bind(size=label.setter('text_size'))
        layout.add_widget(label)

        slider = Slider(
            min=min_val,
            max=max_val,
            step=step,
            value=self.settings[setting_key],
            size_hint_y=None,
            height='40dp'
        )

        def on_slider_value(instance, value, key=setting_key, lbl=label):
            self.update_setting(key, value)
            # Update label text
            base_text = lbl.text.split()[:-1]  # Remove last word (the value)
            lbl.text = f"{' '.join(base_text)} {value:.1f}"

        slider.bind(value=on_slider_value)
        layout.add_widget(slider)

    def update_setting(self, key: str, value: Any):
        """Update a setting value."""
        self.settings[key] = value
        logger.debug(f"Updated setting {key} = {value}")

    def save_settings(self, instance):
        """Save current settings."""
        if self.on_settings_changed:
            self.on_settings_changed(self.settings.copy())
        logger.info("Settings saved")

    def reset_settings(self, instance):
        """Reset settings to defaults."""
        # Reset to default values
        default_settings = {
            'subject_id': '',
            'session_prefix': 'SESS',
            'task_type': 'clothes_sorting',
            'difficulty_level': 2,
            'positioning_mode': 'randomized_fixed',
            'audio_enabled': True,
            'audio_volume': 0.8,
            'trial_timeout': 30.0,
            'inter_trial_interval': 2.0,
            'feedback_duration': 2.0,
            'auto_advance': True,
            'data_export_format': 'csv'
        }

        self.settings.update(default_settings)

        # Rebuild UI to reflect changes
        self.clear_widgets()
        self.setup_ui()

        logger.info("Settings reset to defaults")

    def back_pressed(self, instance):
        """Handle back button press."""
        if self.on_back_pressed:
            self.on_back_pressed()

    def get_settings(self) -> Dict[str, Any]:
        """Get current settings."""
        return self.settings.copy()

    def set_settings(self, settings: Dict[str, Any]):
        """Set settings from external source."""
        self.settings.update(settings)
        # Rebuild UI to reflect changes
        self.clear_widgets()
        self.setup_ui()
        logger.info("Settings updated from external source")
