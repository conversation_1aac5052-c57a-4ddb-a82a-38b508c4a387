# Auto-generated file by setup.py build_factory

from kivy.factory import Factory

r = Factory.register
r('Animation', module='kivy.animation')
r('AnimationTransition', module='kivy.animation')
r('ExceptionHandler', module='kivy.base')
r('Cache', module='kivy.cache')
r('ClockBase', module='kivy.clock')
r('ColorPicker', module='kivy.uix.colorpicker')
r('ColorWheel', module='kivy.uix.colorpicker')
r('ConfigParser', module='kivy.config')
r('EventDispatcher', module='kivy.event')
r('Observable', module='kivy.event')
r('FactoryException', module='kivy.factory')
r('Gesture', module='kivy.gesture')
r('GestureDatabase', module='kivy.gesture')
r('GesturePoint', module='kivy.gesture')
r('GestureStroke', module='kivy.gesture')
r('Parser', module='kivy.lang.parser')
r('LoaderBase', module='kivy.loader')
r('ProxyImage', module='kivy.loader')
r('LoggerHistory', module='kivy.logger')
r('NumericProperty', module='kivy.properties')
r('StringProperty', module='kivy.properties')
r('ListProperty', module='kivy.properties')
r('ObjectProperty', module='kivy.properties')
r('BooleanProperty', module='kivy.properties')
r('BoundedNumericProperty', module='kivy.properties')
r('OptionProperty', module='kivy.properties')
r('ReferenceListProperty', module='kivy.properties')
r('AliasProperty', module='kivy.properties')
r('NumericProperty', module='kivy.properties')
r('DictProperty', module='kivy.properties')
r('VariableListProperty', module='kivy.properties')
r('ConfigParserProperty', module='kivy.properties')
r('ColorProperty', module='kivy.properties')
r('Property', module='kivy.properties')
r('SafeList', module='kivy.utils')
r('Vector', module='kivy.vector')
r('Color', module='kivy.graphics.context_instructions')
r('BindTexture', module='kivy.graphics.context_instructions')
r('PushMatrix', module='kivy.graphics.context_instructions')
r('PopMatrix', module='kivy.graphics.context_instructions')
r('Rotate', module='kivy.graphics.context_instructions')
r('Scale', module='kivy.graphics.context_instructions')
r('Translate', module='kivy.graphics.context_instructions')
r('Transform', module='kivy.graphics.context_instructions')
r('MatrixInstruction', module='kivy.graphics.context_instructions')
r('Fbo', module='kivy.graphics.fbo')
r('Instruction', module='kivy.graphics.instructions')
r('InstructionGroup', module='kivy.graphics.instructions')
r('ContextInstruction', module='kivy.graphics.instructions')
r('VertexInstruction', module='kivy.graphics.instructions')
r('Canvas', module='kivy.graphics.instructions')
r('CanvasBase', module='kivy.graphics.instructions')
r('Callback', module='kivy.graphics.instructions')
r('RenderContext', module='kivy.graphics.instructions')
r('Shader', module='kivy.graphics.shader')
r('Texture', module='kivy.graphics.texture')
r('TextureRegion', module='kivy.graphics.texture')
r('Matrix', module='kivy.graphics.transformation')
r('VBO', module='kivy.graphics.vbo')
r('VertexBatch', module='kivy.graphics.vbo')
r('StencilPush', module='kivy.graphics.stencil_instructions')
r('StencilPop', module='kivy.graphics.stencil_instructions')
r('StencilUse', module='kivy.graphics.stencil_instructions')
r('StencilUnUse', module='kivy.graphics.stencil_instructions')
r('ScissorPush', module='kivy.graphics.scissor_instructions')
r('ScissorPop', module='kivy.graphics.scissor_instructions')
r('Triangle', module='kivy.graphics.vertex_instructions')
r('Quad', module='kivy.graphics.vertex_instructions')
r('Rectangle', module='kivy.graphics.vertex_instructions')
r('RoundedRectangle', module='kivy.graphics.vertex_instructions')
r('BorderImage', module='kivy.graphics.vertex_instructions')
r('Ellipse', module='kivy.graphics.vertex_instructions')
r('Line', module='kivy.graphics.vertex_instructions')
r('SmoothLine', module='kivy.graphics.vertex_instructions')
r('SmoothEllipse', module='kivy.graphics.vertex_instructions')
r('SmoothRectangle', module='kivy.graphics.vertex_instructions')
r('SmoothRoundedRectangle', module='kivy.graphics.vertex_instructions')
r('SmoothTriangle', module='kivy.graphics.vertex_instructions')
r('SmoothQuad', module='kivy.graphics.vertex_instructions')
r('Point', module='kivy.graphics.vertex_instructions')
r('Bezier', module='kivy.graphics.vertex_instructions')
r('Mesh', module='kivy.graphics.vertex_instructions')
r('Svg', module='kivy.graphics.svg')
r('BoxShadow', module='kivy.graphics.boxshadow')
r('MotionEventFactory', module='kivy.input.factory')
r('MotionEventProvider', module='kivy.input.provider')
r('Shape', module='kivy.input.shape')
r('ShapeRect', module='kivy.input.shape')
r('ActionBar', module='kivy.uix.actionbar')
r('ActionItem', module='kivy.uix.actionbar')
r('ActionButton', module='kivy.uix.actionbar')
r('ActionToggleButton', module='kivy.uix.actionbar')
r('ActionCheck', module='kivy.uix.actionbar')
r('ActionSeparator', module='kivy.uix.actionbar')
r('ActionDropDown', module='kivy.uix.actionbar')
r('ActionGroup', module='kivy.uix.actionbar')
r('ActionOverflow', module='kivy.uix.actionbar')
r('ActionView', module='kivy.uix.actionbar')
r('ContextualActionView', module='kivy.uix.actionbar')
r('AnchorLayout', module='kivy.uix.anchorlayout')
r('BoxLayout', module='kivy.uix.boxlayout')
r('GridLayout', module='kivy.uix.gridlayout')
r('PageLayout', module='kivy.uix.pagelayout')
r('Accordion', module='kivy.uix.accordion')
r('AccordionItem', module='kivy.uix.accordion')
r('Button', module='kivy.uix.button')
r('ButtonBehavior', module='kivy.uix.behaviors.button')
r('ToggleButtonBehavior', module='kivy.uix.behaviors.togglebutton')
r('DragBehavior', module='kivy.uix.behaviors.drag')
r('FocusBehavior', module='kivy.uix.behaviors.focus')
r('CompoundSelectionBehavior', module='kivy.uix.behaviors.compoundselection')
r('KNSpaceBehavior', module='kivy.uix.behaviors.knspace')
r('CodeNavigationBehavior', module='kivy.uix.behaviors.codenavigation')
r('TouchRippleBehavior', module='kivy.uix.behaviors.touchripple')
r('TouchRippleButtonBehavior', module='kivy.uix.behaviors.touchripple')
r('EmacsBehavior', module='kivy.uix.behaviors.emacs')
r('CoverBehavior', module='kivy.uix.behaviors.cover')
r('Bubble', module='kivy.uix.bubble')
r('BubbleButton', module='kivy.uix.bubble')
r('Camera', module='kivy.uix.camera')
r('Carousel', module='kivy.uix.carousel')
r('CodeInput', module='kivy.uix.codeinput')
r('CheckBox', module='kivy.uix.checkbox')
r('DropDown', module='kivy.uix.dropdown')
r('EffectWidget', module='kivy.uix.effectwidget')
r('FloatLayout', module='kivy.uix.floatlayout')
r('RelativeLayout', module='kivy.uix.relativelayout')
r('ScatterLayout', module='kivy.uix.scatterlayout')
r('ScatterPlaneLayout', module='kivy.uix.scatterlayout')
r('FileChooserListView', module='kivy.uix.filechooser')
r('FileChooserIconView', module='kivy.uix.filechooser')
r('FileChooser', module='kivy.uix.filechooser')
r('Image', module='kivy.uix.image')
r('AsyncImage', module='kivy.uix.image')
r('Label', module='kivy.uix.label')
r('Layout', module='kivy.uix.layout')
r('ModalView', module='kivy.uix.modalview')
r('ProgressBar', module='kivy.uix.progressbar')
r('Popup', module='kivy.uix.popup')
r('Scatter', module='kivy.uix.scatter')
r('ScatterPlane', module='kivy.uix.scatter')
r('ScrollView', module='kivy.uix.scrollview')
r('Settings', module='kivy.uix.settings')
r('Slider', module='kivy.uix.slider')
r('Screen', module='kivy.uix.screenmanager')
r('ScreenManager', module='kivy.uix.screenmanager')
r('Spinner', module='kivy.uix.spinner')
r('Splitter', module='kivy.uix.splitter')
r('StackLayout', module='kivy.uix.stacklayout')
r('StencilView', module='kivy.uix.stencilview')
r('Switch', module='kivy.uix.switch')
r('TabbedPanel', module='kivy.uix.tabbedpanel')
r('TabbedPanelHeader', module='kivy.uix.tabbedpanel')
r('TextInput', module='kivy.uix.textinput')
r('ToggleButton', module='kivy.uix.togglebutton')
r('TreeView', module='kivy.uix.treeview')
r('TreeViewLabel', module='kivy.uix.treeview')
r('TreeViewNode', module='kivy.uix.treeview')
r('ShaderTransition', module='kivy.uix.screenmanager')
r('SlideTransition', module='kivy.uix.screenmanager')
r('SwapTransition', module='kivy.uix.screenmanager')
r('WipeTransition', module='kivy.uix.screenmanager')
r('FadeTransition', module='kivy.uix.screenmanager')
r('Sandbox', module='kivy.uix.sandbox')
r('Video', module='kivy.uix.video')
r('VideoPlayer', module='kivy.uix.videoplayer')
r('VideoPlayerVolume', module='kivy.uix.videoplayer')
r('VideoPlayerStop', module='kivy.uix.videoplayer')
r('VideoPlayerPlayPause', module='kivy.uix.videoplayer')
r('VideoPlayerProgressBar', module='kivy.uix.videoplayer')
r('VKeyboard', module='kivy.uix.vkeyboard')
r('Widget', module='kivy.uix.widget')
r('WidgetException', module='kivy.uix.widget')
r('RstDocument', module='kivy.uix.rst')
r('KineticEffect', module='kivy.effects.kinetic')
r('ScrollEffect', module='kivy.effects.scroll')
r('DampedScrollEffect', module='kivy.effects.dampedscroll')
r('OpacityScrollEffect', module='kivy.effects.opacityscroll')
r('Recognizer', module='kivy.multistroke')
r('MultistrokeGesture', module='kivy.multistroke')
r('UnistrokeTemplate', module='kivy.multistroke')
r('ProgressTracker', module='kivy.multistroke')
r('GestureSurface', module='kivy.uix.gesturesurface')
r('GestureContainer', module='kivy.uix.gesturesurface')
r('RecycleViewBehavior', module='kivy.uix.recycleview.__init__')
r('RecycleView', module='kivy.uix.recycleview.__init__')
r('LayoutSelectionBehavior', module='kivy.uix.recycleview.layout')
r('RecycleLayoutManagerBehavior', module='kivy.uix.recycleview.layout')
r('RecycleDataViewBehavior', module='kivy.uix.recycleview.views')
r('RecycleKVIDsDataViewBehavior', module='kivy.uix.recycleview.views')
r('RecycleDataAdapter', module='kivy.uix.recycleview.views')
r('RecycleDataModelBehavior', module='kivy.uix.recycleview.datamodel')
r('RecycleDataModel', module='kivy.uix.recycleview.datamodel')
r('RecycleLayout', module='kivy.uix.recyclelayout')
r('RecycleGridLayout', module='kivy.uix.recyclegridlayout')
r('RecycleBoxLayout', module='kivy.uix.recycleboxlayout')
