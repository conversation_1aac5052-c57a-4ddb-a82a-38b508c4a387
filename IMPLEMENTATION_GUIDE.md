# DTT Response Strength Analysis System - Implementation Guide

## Overview

This is a complete implementation of the DTT (Discrete Trial Training) Response Strength Analysis System as specified in the README.MD. The system provides:

- **Data Collection**: User-friendly DTT interface using <PERSON><PERSON> for clothes sorting tasks
- **Precise Timing**: Millisecond-accurate capture of trial events and responses
- **Data Storage**: Robust SQLite database for trial, session, and subject data
- **Response Strength Analysis**: R-based statistical analysis using the Killeen et al. (2002) framework
- **Reporting**: Data export and analysis visualization capabilities

## What's Been Implemented

### ✅ Core Infrastructure
- Complete SQLite database schema with CRUD operations
- Precise timing utilities using `time.perf_counter()`
- Audio feedback system with placeholder sound files
- Comprehensive logging and error handling

### ✅ GUI Framework
- **Main Screen**: Subject selection, session management, analysis triggers
- **Trial Screen**: Interactive DTT task presentation with touch response collection
- **Settings Screen**: Configurable application parameters
- **Screen Management**: Kivy-based navigation between screens

### ✅ DTT Tasks
- **Base Task Class**: Abstract framework for all DTT tasks
- **Clothes Sorting Task**: Complete implementation with:
  - Color-based stimulus generation (red, blue, green, yellow)
  - Touch-based response collection
  - Real-time feedback and scoring
  - Configurable difficulty levels

### ✅ R Analysis Framework
- Complete implementation of Killeen et al. (2002) analysis functions:
  - Markov chain analysis of response sequences
  - Latency distribution fitting (exponential, gamma, lognormal, Weibull)
  - Inter-response time (IRT) analysis
  - Rate-probability mapping with epoch sampling
  - Statistical testing of rate-probability relationships
- Python-R integration via subprocess calls
- Command-line argument parsing for flexible analysis

### ✅ Data Management
- Trial data export to CSV, JSON formats
- Session summaries and subject reports
- R-compatible data formatting
- Database integrity and validation

## Installation and Setup

### 1. Python Environment
```bash
# Create virtual environment
python -m venv dtt_env

# Activate environment (Windows)
dtt_env\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Install Kivy dependencies (Windows)
pip install kivy-deps.angle kivy-deps.glew kivy-deps.gstreamer
```

### 2. R Environment
```r
# Install required R packages
install.packages(c("RSQLite", "DBI", "tidyverse", "markovchain",
                   "fitdistrplus", "jsonlite", "optparse"))
```

### 3. Verify Installation
```bash
# Test the system
python test_system.py

# Test database functionality
python src/data/database.py

# Test R connectivity
python src/main.py --simulate
```

## Running the Application

### GUI Mode (Default)
```bash
python src/main.py
```

### Simulation Mode (No GUI)
```bash
python src/main.py --simulate
```

### R Analysis Only
```bash
Rscript analysis/response_strength_analysis.R --subject SUB001 --db_path assets/data/dtt_data.db
```

## Usage Guide

### 1. Starting a Session
1. Launch the application: `python src/main.py`
2. Click "Select Subject" to create or choose a participant
3. Click "Start New Session" to begin DTT trials
4. The system switches to the trial screen automatically

### 2. Running Trials
1. Click "Start Trial" to begin each trial
2. A colored clothing item appears in the center
3. Touch the matching color bin to respond
4. Receive immediate feedback (correct/incorrect)
5. Repeat for desired number of trials
6. Click "End Session" when complete

### 3. Data Analysis
1. Return to main screen after session
2. Click "Run Analysis" to trigger R analysis
3. Results are logged and can be exported
4. Use "Test R Connection" to verify R setup

### 4. Settings Configuration
1. Click "Settings" from main screen
2. Adjust task difficulty, timing parameters, audio settings
3. Configure data export preferences
4. Save settings to apply changes

## Architecture Overview

```
DTT System Architecture
├── Frontend (Kivy GUI)
│   ├── MainScreen - Navigation and subject management
│   ├── TrialScreen - Task presentation and response collection
│   └── SettingsScreen - Configuration management
├── Backend (Python)
│   ├── Database Layer - SQLite operations and data persistence
│   ├── Task Framework - Abstract base classes and specific implementations
│   ├── Timing System - High-precision event timing
│   └── Audio System - Feedback sound management
├── Analysis (R)
│   ├── Killeen Framework - Statistical analysis functions
│   ├── Data Loading - Database connectivity and preprocessing
│   └── Export Functions - Results formatting and output
└── Integration
    ├── Python-R Bridge - Subprocess communication
    ├── Data Export - Multi-format data output
    └── Configuration - Settings management
```

## Key Features

### Precise Timing
- Millisecond-accurate timestamps using `time.perf_counter()`
- Event-based timing system for trial phases
- Automatic latency and duration calculations

### Flexible Task Framework
- Abstract base class for easy task extension
- Configurable difficulty levels
- Touch-based response collection
- Real-time feedback system

### Robust Data Storage
- SQLite database with proper schema design
- JSON storage for complex data (response times, stimulus parameters)
- Automatic data validation and integrity checks
- Export capabilities for external analysis

### Advanced Analytics
- Complete Killeen et al. (2002) framework implementation
- Multiple distribution fitting for latencies and IRTs
- Rate-probability relationship analysis
- Markov chain modeling of response patterns

## Asset Management

### Adding Visual Assets to the Database

The DTT system now uses actual image assets for the clothes sorting task. Here's how to properly add and configure assets:

#### 1. Image Assets Structure
```
assets/
├── images/
│   ├── Laundry_Basket_White.png    # White clothes basket
│   ├── Laundry_Basket_Black.png    # Dark clothes basket
│   ├── Laundry_Basket_Color.png    # Colored clothes basket
│   ├── Shirt_White.png             # White shirt stimulus
│   ├── Shirt_Dark_Blue.png         # Dark shirt stimulus
│   ├── Shirt_Grey.png              # Dark shirt stimulus
│   ├── Shirt_Teal.png              # Light colored shirt stimulus
│   └── Shirt_Yellow.png            # Light colored shirt stimulus
└── sounds/
    ├── correct.wav                 # Correct response feedback
    └── incorrect.wav               # Incorrect response feedback
```

#### 2. Adding New Shirt Stimuli
To add new shirt images to the clothes sorting task:

1. **Add image file** to `assets/images/` directory
2. **Update the task configuration** in `src/tasks/clothes_sorting.py`:

```python
# In ClothesSortingTask.__init__()
self.shirt_stimuli = {
    "Shirt_White.png": "white",
    "Shirt_Dark_Blue.png": "black",
    "Shirt_Grey.png": "black",
    "Shirt_Teal.png": "color",
    "Shirt_Yellow.png": "color",
    "Shirt_New_Red.png": "color",      # Add new shirt here
    "Shirt_New_Black.png": "black",   # Add new shirt here
}
```

3. **Categorization Rules**:
   - `"white"` → White basket (for white/cream colored items)
   - `"black"` → Black basket (for dark colored items: black, navy, dark gray, etc.)
   - `"color"` → Color basket (for light/bright colored items: red, yellow, teal, etc.)

#### 3. Adding New Basket Types
To add additional basket categories:

1. **Add basket image** to `assets/images/`
2. **Update basket configuration**:

```python
# In ClothesSortingTask.__init__()
self.basket_info = {
    "white": {
        "image": "Laundry_Basket_White.png",
        "label": "White Clothes",
        "category": "white"
    },
    "black": {
        "image": "Laundry_Basket_Black.png",
        "label": "Dark Clothes",
        "category": "black"
    },
    "color": {
        "image": "Laundry_Basket_Color.png",
        "label": "Colored Clothes",
        "category": "color"
    },
    "new_category": {                    # Add new basket here
        "image": "Laundry_Basket_New.png",
        "label": "New Category",
        "category": "new_category"
    }
}
```

3. **Update positioning logic** in `_update_basket_positions()` if needed

#### 4. Asset Validation
The system automatically validates that all required assets exist:

```python
# Check asset validation
task = ClothesSortingTask()
validation = task.validate_assets()

if validation['all_valid']:
    print("All assets found!")
else:
    print("Missing assets:")
    for category, assets in validation.items():
        if category != 'all_valid':
            for asset, exists in assets.items():
                if not exists:
                    print(f"  Missing: {asset}")
```

#### 5. Basket Positioning Modes
The system supports three positioning modes configurable in settings:

- **`fixed`**: Always same order (White, Black, Color from left to right)
- **`randomized_fixed`**: Random order determined at session start, stays consistent throughout session
- **`random`**: New random positions for each trial

Configure in Settings screen or programmatically:
```python
task = ClothesSortingTask(positioning_mode="randomized_fixed")
```

#### 6. Audio Assets
Audio feedback files should be placed in `assets/sounds/`:
- `correct.wav` - Played for correct responses
- `incorrect.wav` - Played for incorrect responses

The system supports WAV format and will create placeholder files if assets are missing.

## Extending the System

### Adding New Tasks
1. Create new task class inheriting from `BaseTask`
2. Implement required abstract methods:
   - `generate_stimulus()`
   - `present_stimulus()`
   - `evaluate_response()`
   - `cleanup_trial()`
3. Add task to `src/tasks/__init__.py`
4. Update main application to include new task option

### Adding Analysis Functions
1. Add new R functions to `analysis/killeen_framework.R`
2. Update `analyze_response_strength_r()` to call new functions
3. Modify data export if new data formats needed
4. Test with existing data to ensure compatibility

### Customizing GUI
1. Modify existing screen classes in `src/gui/`
2. Add new screens by creating new classes inheriting from Kivy layouts
3. Update screen manager in `src/main.py`
4. Implement navigation callbacks

## Testing and Validation

### Automated Testing
```bash
# Run comprehensive system tests
python test_system.py

# Test individual components
python src/data/database.py
python -c "from src.utils.timing import TimingManager; tm = TimingManager(); print('Timing OK')"
```

### Manual Testing
1. Run complete DTT sessions with known responses
2. Verify timing accuracy using external timing tools
3. Compare R analysis results with manual calculations
4. Test error handling with invalid inputs

### Performance Testing
1. Large dataset analysis (1000+ trials)
2. Memory usage monitoring during long sessions
3. GUI responsiveness under load
4. Database performance with multiple subjects

## Troubleshooting

### Common Issues

**Kivy Import Errors**
- Ensure all Kivy dependencies are installed
- Check virtual environment activation
- Install platform-specific Kivy packages

**R Connection Failures**
- Verify R is installed and in PATH
- Check R package installations
- Test R script independently: `Rscript analysis/killeen_framework.R --help`

**Database Errors**
- Check file permissions in `assets/data/` directory
- Verify SQLite installation
- Run database tests: `python src/data/database.py`

**Audio Issues**
- Audio system uses placeholder files by default
- Check `assets/sounds/` directory exists
- Install `plyer` for platform-specific audio support

### Performance Optimization
- Use `--simulate` mode for batch processing
- Export data in chunks for large datasets
- Monitor memory usage during long sessions
- Consider database indexing for large datasets

## Future Enhancements

### Planned Features
- Additional DTT tasks (shape sorting, number matching)
- Real-time analysis dashboard
- Multi-user session management
- Cloud data synchronization
- Advanced reporting with visualizations

### Research Extensions
- Machine learning response prediction
- Adaptive difficulty adjustment
- Real-time intervention recommendations
- Integration with other ABA data systems

## Support and Documentation

- **System Architecture**: See `README.MD` for detailed specifications
- **API Documentation**: Inline docstrings in all modules
- **Database Schema**: See `src/data/database.py` for table definitions
- **R Functions**: See `analysis/killeen_framework.R` for analysis details

For technical support or feature requests, refer to the project documentation and logging output for debugging information.
