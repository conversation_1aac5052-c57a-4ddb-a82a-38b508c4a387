#!/usr/bin/env python3
"""
Test script for DTT Response Strength Analysis System
"""

import sys
import os
import logging

# Add src to path
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

# Change to src directory for relative imports
original_cwd = os.getcwd()
os.chdir(src_path)

from data import database
from tasks.clothes_sorting import ClothesSortingTask
from tasks.base_task import TrialResult
from utils.timing import TimingManager
from utils.audio import AudioManager

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_database():
    """Test database functionality."""
    logger.info("Testing database functionality...")

    # Initialize database
    database.create_tables()

    # Test subject creation
    subject_id = "TEST001"
    success = database.add_subject(subject_id, "Test Subject")
    logger.info(f"Subject creation: {'SUCCESS' if success else 'FAILED'}")

    # Test subject retrieval
    subject = database.get_subject(subject_id)
    logger.info(f"Subject retrieval: {'SUCCESS' if subject else 'FAILED'}")

    # Test session creation
    session_id = "TESTSESS001"
    success = database.start_session(session_id, subject_id, "2024-01-01", "2024-01-01T10:00:00")
    logger.info(f"Session creation: {'SUCCESS' if success else 'FAILED'}")

    # Test trial creation
    trial_id = database.add_trial(
        subject_id=subject_id,
        session_id=session_id,
        trial_number=1,
        task_type="test_task",
        target_skill="test_skill",
        trial_start_time=1000.0,
        sd_presentation_time=1500.0,
        trial_end_time=5000.0,
        response_occurred=1,
        first_response_time=2000.0,
        all_response_times=[2000.0],
        response_correct=1
    )
    logger.info(f"Trial creation: {'SUCCESS' if trial_id else 'FAILED'}")

    # Test trial retrieval
    trials = database.get_trials_for_session(session_id)
    logger.info(f"Trial retrieval: {'SUCCESS' if trials else 'FAILED'}")

    logger.info("Database tests completed")


def test_timing():
    """Test timing functionality."""
    logger.info("Testing timing functionality...")

    timing_manager = TimingManager()

    # Test session timing
    session_start = timing_manager.start_session()
    logger.info(f"Session start: {'SUCCESS' if session_start else 'FAILED'}")

    # Test trial timing
    trial_start = timing_manager.start_trial()
    logger.info(f"Trial start: {'SUCCESS' if trial_start else 'FAILED'}")

    # Test event marking
    sd_time = timing_manager.mark_event('sd_presentation')
    response_time = timing_manager.mark_event('first_response')
    trial_end = timing_manager.end_trial()

    # Test calculations
    latency = timing_manager.get_latency()
    duration = timing_manager.get_trial_duration()

    logger.info(f"Latency calculation: {'SUCCESS' if latency is not None else 'FAILED'}")
    logger.info(f"Duration calculation: {'SUCCESS' if duration is not None else 'FAILED'}")

    logger.info("Timing tests completed")


def test_audio():
    """Test audio functionality."""
    logger.info("Testing audio functionality...")

    audio_manager = AudioManager()

    # Test feedback sounds (will create placeholder files)
    correct_result = audio_manager.play_correct_feedback()
    incorrect_result = audio_manager.play_incorrect_feedback()

    logger.info(f"Audio system: {'SUCCESS' if True else 'FAILED'}")  # Always succeeds with placeholders
    logger.info("Audio tests completed")


def test_clothes_sorting_task():
    """Test clothes sorting task."""
    logger.info("Testing clothes sorting task...")

    task = ClothesSortingTask()

    # Test task initialization
    logger.info(f"Task initialization: {'SUCCESS' if task.task_name == 'clothes_sorting' else 'FAILED'}")

    # Test stimulus generation
    stimulus = task.generate_stimulus(1)
    logger.info(f"Stimulus generation: {'SUCCESS' if stimulus and 'color' in stimulus else 'FAILED'}")

    # Test response evaluation
    correct_response = {'selected_bin': stimulus['correct_bin']}
    incorrect_response = {'selected_bin': 'wrong_color'}

    correct_result = task.evaluate_response(correct_response, stimulus)
    incorrect_result = task.evaluate_response(incorrect_response, stimulus)

    logger.info(f"Correct response evaluation: {'SUCCESS' if correct_result == TrialResult.CORRECT else 'FAILED'}")
    logger.info(f"Incorrect response evaluation: {'SUCCESS' if incorrect_result == TrialResult.INCORRECT else 'FAILED'}")

    # Test touch handling
    touch_result = task.handle_touch((0.2, 0.3))  # Should hit red bin
    logger.info(f"Touch handling: {'SUCCESS' if 'selected_bin' in touch_result else 'FAILED'}")

    logger.info("Clothes sorting task tests completed")


def test_full_trial_flow():
    """Test a complete trial flow."""
    logger.info("Testing full trial flow...")

    # Initialize components
    database.create_tables()
    task = ClothesSortingTask()

    # Create test subject
    subject_id = "FLOW_TEST"
    database.add_subject(subject_id, "Flow Test Subject")

    # Start session
    task.start_session()

    # Run a trial
    trial_data = task.start_trial()
    logger.info(f"Trial start: {'SUCCESS' if trial_data else 'FAILED'}")

    # Present stimulus
    sd_time = task.present_trial_stimulus(trial_data)
    logger.info(f"Stimulus presentation: {'SUCCESS' if sd_time else 'FAILED'}")

    # Simulate response
    response_data = {'selected_bin': trial_data.stimulus_data['correct_bin']}
    result = task.record_response(response_data, trial_data)
    logger.info(f"Response recording: {'SUCCESS' if result == TrialResult.CORRECT else 'FAILED'}")

    # Complete trial
    task.complete_trial(trial_data)
    logger.info(f"Trial completion: {'SUCCESS' if trial_data.result == TrialResult.CORRECT else 'FAILED'}")

    # End session
    task.end_session()

    # Check session summary
    summary = task.get_session_summary()
    logger.info(f"Session summary: {'SUCCESS' if summary['total_trials'] == 1 else 'FAILED'}")

    logger.info("Full trial flow test completed")


def main():
    """Run all tests."""
    logger.info("Starting DTT System Tests...")

    try:
        test_database()
        test_timing()
        test_audio()
        test_clothes_sorting_task()
        test_full_trial_flow()

        logger.info("All tests completed successfully!")

    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
