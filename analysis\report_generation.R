# analysis/report_generation.R

# --- Load necessary packages ---
suppressPackageStartupMessages({
    library(rmarkdown)
    library(knitr)
    library(tidyverse) # For data manipulation and ggplot2 if used in Rmd
    library(jsonlite)  # If results are passed as JSON
    library(optparse)  # For command line arguments
    library(kableExtra) # For better tables
})

# --- Logging setup ---
log_info <- function(...) { message(paste0("[INFO_REPORT] ", Sys.time(), " | ", ...)) }
log_warn <- function(...) { message(paste0("[WARN_REPORT] ", Sys.time(), " | ", ...)) }
log_error <- function(...) { message(paste0("[ERROR_REPORT] ", Sys.time(), " | ", ...)) }

# --- Argument Parsing ---
option_list <- list(
    make_option(c("-i", "--input_data"), type="character", default=NULL,
                help="Path to the Rds file containing analysis results from killeen_framework.R OR path to a JSON file.",
                metavar="character"),
    make_option(c("-f", "--input_format"), type="character", default="rds",
                help="Format of the input data: 'rds' or 'json' [default %default]",
                metavar="character"),
    make_option(c("-o", "--output_report"), type="character", default=paste0("DTT_Analysis_Report_", format(Sys.Date(), "%Y%m%d"), ".html"),
                help="Filename for the output report (e.g., report.html, report.pdf) [default %default]",
                metavar="character"),
    make_option(c("-t", "--output_format"), type="character", default="html_document",
                help="Output format for rmarkdown::render (e.g., 'html_document', 'pdf_document', 'word_document') [default %default]",
                metavar="character"),
    make_option(c("-s", "--subject_id"), type="character", default=NULL,
                help="Optional: Specific Subject ID to generate report for (if input contains multiple subjects)",
                metavar="character")
)
opt_parser <- OptionParser(option_list=option_list)
opt <- parse_args(opt_parser)

if (is.null(opt$input_data)) {
    log_error("Input data file path (--input_data) must be supplied.")
    stop("Input data path not specified.", call.=FALSE)
}
if (!file.exists(opt$input_data)) {
    log_error("Input data file not found: ", opt$input_data)
    stop("Input data file not found.", call.=FALSE)
}

# --- Helper Functions for R Markdown Content ---

format_markov_params <- function(markov_results) {
    if (is.null(markov_results)) return("No Markov analysis data available.")
    
    p_1_given_1 <- round(markov_results$p_1_given_1, 3)
    p_1_given_0 <- round(markov_results$p_1_given_0, 3)
    base_prob <- round(markov_results$base_prob, 3)
    theoretical_pi <- round(markov_results$theoretical_pi, 3)
    
    steady_0 <- round(markov_results$steady_state[1, "0"], 3)
    steady_1 <- round(markov_results$steady_state[1, "1"], 3)

    content <- paste0(
        "- **P(Response | Prev. Response) (p):** ", p_1_given_1, "\n",
        "- **P(Response | Prev. No Response) (q):** ", p_1_given_0, "\n",
        "- **Overall P(Response) (π empirical):** ", base_prob, "\n",
        "- **Overall P(Response) (π theoretical):** ", theoretical_pi, "\n",
        "- **Steady State P(No Response):** ", steady_0, "\n",
        "- **Steady State P(Response):** ", steady_1, "\n"
    )
    # Add transition matrix if desired
    # content <- paste0(content, "\nTransition Matrix:\n", kable(markov_results$transition_matrix) %>% kable_styling())
    return(content)
}

format_latency_params <- function(latency_results) {
    if (is.null(latency_results)) return("No latency analysis data available.")
    
    model_name <- latency_results$best_model_type
    params <- latency_results$params
    
    param_str <- ""
    if (model_name == "Erlang") {
        param_str <- paste0("n = ", round(params$n, 2), ", τ (period) = ", round(params$tau, 3), " s")
    } else if (model_name == "Gamma") {
        param_str <- paste0("Shape = ", round(params$shape, 3), ", Rate = ", round(params$rate, 3))
    } else if (grepl("Gumbel", model_name, ignore.case=TRUE)) {
        param_str <- paste0("μ (location) = ", round(params$mu, 3), " s, σ (scale) = ", round(params$sigma, 3), " s")
    } else {
        param_str <- "Parameters not formatted for this model type."
    }
    
    content <- paste0(
        "- **Best Fit Model:** ", model_name, "\n",
        "- **Parameters:** ", param_str, "\n",
        "- **AIC (Gamma):** ", round(latency_results$aic["gamma"],1), 
        ", **AIC (Gumbel):** ", round(latency_results$aic["gumbel"],1) ,"\n"
    )
    return(content)
}

format_irt_params <- function(irt_results) {
    if (is.null(irt_results)) return("No IRT analysis data available.")
    
    model_name <- irt_results$model_type
    params <- irt_results$params
    
    param_str <- ""
    if (model_name == "Exponential") {
        param_str <- paste0("λ (rate) = ", round(params$lambda, 3), " /s")
    } else if (model_name == "Refractory Poisson") {
        param_str <- paste0("λ (initiation rate) = ", round(params$lambda, 3), " /s, δ (refractory period) = ", round(params$delta, 3), " s")
    } else if (grepl("Palya-like", model_name, ignore.case=TRUE)) {
        param_str <- paste0("Periodicity detected. Estimated period lag (samples): ", 
                           irt_results$palya_params$period_estimate_lag, 
                           ". Full Palya model fitting not implemented in this report.")
    } else {
         param_str <- "Parameters not formatted for this model type or no model fit."
    }
    
    aic_str <- ""
    if(!is.null(irt_results$aic) && length(irt_results$aic) > 0) {
        aic_vals <- sapply(names(irt_results$aic), function(n) paste0(n, ": ", round(irt_results$aic[[n]],1)))
        aic_str <- paste0("- **AIC Values:** ", paste(aic_vals, collapse=", "), "\n")
    }

    content <- paste0(
        "- **Best Fit Model/Type:** ", model_name, "\n",
        "- **Parameters:** ", param_str, "\n",
        aic_str
    )
    return(content)
}

format_rate_prob_params <- function(rp_results) {
    if (is.null(rp_results) || is.null(rp_results$best_model_type)) return("No rate-probability analysis data available.")
    
    model_name <- rp_results$best_model_type
    params <- rp_results$params
    
    param_str <- ""
    if (grepl("Linear", model_name, ignore.case=TRUE)) {
        param_str <- paste0("k (slope, p=k*b*Δt) = ", round(params$linear_coefficient_k, 3))
    } else if (grepl("Exponential", model_name, ignore.case=TRUE)) {
        param_str <- paste0("k_exp (factor, p=1-exp(-k_exp*b*Δt)) = ", round(params$k_exp_factor, 3))
    } else if (grepl("Refractory Poisson", model_name, ignore.case=TRUE)) {
        param_str <- paste0("δ (refractory period) = ", round(params$delta_refractory, 3), " s",
                           " (Avg. est. initiation rate λ = ", round(params$avg_initiation_rate_lambda, 3), " /s)")
    } else {
        param_str <- "Parameters not formatted for this model type."
    }
    
    aic_str <- ""
    if(!is.null(rp_results$aic_values) && length(rp_results$aic_values) > 0) {
        aic_vals <- sapply(names(rp_results$aic_values), function(n) paste0(n, ": ", round(rp_results$aic_values[[n]],1)))
        aic_str <- paste0("- **AIC Values:** ", paste(aic_vals, collapse=", "), "\n")
    }

    content <- paste0(
        "- **Best Fit Model:** ", model_name, "\n",
        "- **Parameters:** ", param_str, "\n",
        aic_str
    )
    # Could add plot of empirical vs fitted later
    return(content)
}


# --- Main Report Generation Logic ---
main_report <- function() {
    log_info("--- DTT Response Strength Analysis Report Generation Started ---")
    log_info("Input data file: ", opt$input_data)
    log_info("Input format: ", opt$input_format)
    log_info("Output report file: ", opt$output_report)
    log_info("Output format: ", opt$output_format)
    if (!is.null(opt$subject_id)) {
        log_info("Generating report for specific subject: ", opt$subject_id)
    }

    # Load the analysis results
    analysis_data <- NULL
    if (tolower(opt$input_format) == "rds") {
        analysis_data <- tryCatch(readRDS(opt$input_data), error = function(e) {
            log_error("Failed to read RDS input file: ", e$message)
            NULL
        })
    } else if (tolower(opt$input_format) == "json") {
        analysis_data <- tryCatch(jsonlite::fromJSON(txt = opt$input_data, simplifyVector = FALSE), error = function(e) {
            log_error("Failed to read JSON input file: ", e$message)
            NULL
        })
    } else {
        log_error("Unsupported input format: ", opt$input_format)
    }

    if (is.null(analysis_data)) {
        stop("Could not load analysis data.", call.=FALSE)
    }

    # Filter for a specific subject if requested
    subjects_to_report <- names(analysis_data$by_subject) # Assuming this structure from Killeen script
    if (!is.null(opt$subject_id)) {
        target_subject_key <- paste0("subject_", opt$subject_id)
        if (target_subject_key %in% subjects_to_report) {
            subjects_to_report <- target_subject_key
            log_info("Reporting only for subject: ", opt$subject_id)
        } else {
            log_warn("Requested subject_id '", opt$subject_id, "' not found in the analysis data. Reporting for all subjects.")
        }
    }
    
    # Start R Markdown content
    rmd_content <- paste0(
        "---\n",
        "title: \"DTT Response Strength Analysis Report\"\n",
        "author: \"Automated System\"\n",
        "date: \"", Sys.Date(), "\"\n",
        "output: ", opt$output_format, "\n",
        "---\n\n",
        "# Overview\n",
        "This report summarizes the response strength analysis based on the Killeen et al. (2002) framework.\n",
        "Data processed from: `", basename(opt$input_data), "`\n\n"
    )

    if (!is.null(analysis_data$aggregate)) {
        rmd_content <- paste0(rmd_content,
            "## Aggregate Summary (Across ", analysis_data$aggregate$n_subjects, " Subjects)\n",
            "### Markov Model (Averages):\n",
            "- Mean P(Response | Prev. Response) (p): ", round(analysis_data$aggregate$markov$mean_p_1_given_1, 3), "\n",
            "- Mean P(Response | Prev. No Response) (q): ", round(analysis_data$aggregate$markov$mean_p_1_given_0, 3), "\n",
            "- Mean Overall P(Response) (π empirical): ", round(analysis_data$aggregate$markov$mean_base_prob, 3), "\n\n",
            "### Model Fit Counts:\n",
            "**Latency Models:**\n", paste0("- ", names(analysis_data$aggregate$model_type_counts$latency), ": ", analysis_data$aggregate$model_type_counts$latency, collapse="\n"), "\n\n",
            "**IRT Models:**\n", paste0("- ", names(analysis_data$aggregate$model_type_counts$irt), ": ", analysis_data$aggregate$model_type_counts$irt, collapse="\n"), "\n\n",
            "**Rate-Probability Models:**\n", paste0("- ", names(analysis_data$aggregate$model_type_counts$rate_probability), ": ", analysis_data$aggregate$model_type_counts$rate_probability, collapse="\n"), "\n\n"
        )
    }


    # Iterate through subjects to report
    for (subject_key in subjects_to_report) {
        subject_id_val <- sub("subject_", "", subject_key)
        subj_data <- analysis_data$by_subject[[subject_key]]

        if (is.null(subj_data)) {
            log_warn("No data found for subject key: ", subject_key)
            next
        }

        rmd_content <- paste0(rmd_content,
            "\n# Analysis for Subject: ", subject_id_val, "\n\n",
            "## 1. Two-State Markov Model (Trial Responding)\n",
            format_markov_params(subj_data$markov), "\n\n",
            "## 2. Response Latency Distribution\n",
            format_latency_params(subj_data$latency), "\n\n",
            "## 3. Inter-Response Time (IRT) Distribution\n",
            format_irt_params(subj_data$irt), "\n\n",
            "## 4. Rate-Probability Mapping\n",
            format_rate_prob_params(subj_data$rate_probability), "\n\n"
            # Add placeholders for plots if they were generated and paths saved
        )
    }

    # Create a temporary Rmd file
    temp_rmd_file <- tempfile(fileext = ".Rmd")
    writeLines(rmd_content, temp_rmd_file)
    log_info("Temporary R Markdown file created at: ", temp_rmd_file)

    # Render the Rmd file
    output_file_abs <- normalizePath(opt$output_report, mustWork = FALSE)
    output_dir <- dirname(output_file_abs)
    if (!dir.exists(output_dir)) {
        dir.create(output_dir, recursive = TRUE)
        log_info("Created output directory: ", output_dir)
    }
    
    tryCatch({
        rmarkdown::render(temp_rmd_file,
                          output_file = basename(output_file_abs), # render in output_dir
                          output_dir = output_dir,
                          output_format = opt$output_format,
                          quiet = FALSE) # Set to TRUE for less console noise
        log_info("Report successfully generated: ", output_file_abs)
    }, error = function(e) {
        log_error("Failed to render R Markdown report: ", e$message)
        log_error("Rmd content was written to: ", temp_rmd_file, " - You can try rendering it manually.")
    })

    # Optional: Clean up temporary Rmd file
    # if (file.exists(temp_rmd_file)) file.remove(temp_rmd_file)

    log_info("--- DTT Response Strength Analysis Report Generation Finished ---")
}


# --- Execute Main ---
if (!interactive()){
    main_report()
}