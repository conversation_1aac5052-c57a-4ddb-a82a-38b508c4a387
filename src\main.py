# src/main.py
import os
import sys

# Disable Kivy argument parsing to allow our own command line arguments
os.environ['KIVY_NO_ARGS'] = '1'

import logging
import time
import uuid
import datetime
import subprocess # For calling R
import pandas as pd
import json # For passing complex data to R if needed

from data import database # Our database module

# --- Kivy Imports ---
from kivy.app import App
from kivy.uix.screenmanager import ScreenManager, Screen
from kivy.uix.label import Label
from gui.main_screen import MainScreen
from gui.trial_screen import TrialScreen
from gui.settings_screen import SettingsScreen

# --- Logging Setup ---
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
logging.basicConfig(level=logging.DEBUG, format=LOG_FORMAT)
logger = logging.getLogger(__name__)


R_SCRIPT_PATH = "analysis/response_strength_analysis.R" # Relative to project root

def initialize_system():
    """Initializes the system, e.g., creates database tables."""
    logger.info("DTT Response Strength Analysis System - Initializing...")
    database.create_tables()
    logger.info("System Initialized.")

def run_dtt_session(subject_id, session_id_prefix="SESS", _import_system_random=None):
    """Simulates a DTT session and records data."""
    if _import_system_random is None:
        _import_system_random = __import__("random")

    logger.info(f"--- Starting DTT Session for Subject: {subject_id} ---")
    session_id = f"{session_id_prefix}{uuid.uuid4().hex[:8].upper()}"
    session_date = datetime.date.today().isoformat()
    start_time_dt = datetime.datetime.now()
    start_time_iso = start_time_dt.isoformat()

    database.start_session(session_id, subject_id, session_date, start_time_iso, ["simulated_task"])

    num_trials = 10 # Simulate 10 trials
    trial_data_for_r = [] # To collect data for R analysis

    for i in range(1, num_trials + 1):
        logger.debug(f"Running Trial {i} for session {session_id}")
        trial_start_ts = time.time() * 1000
        time.sleep(0.5) # Simulate SD presentation delay
        sd_presentation_ts = time.time() * 1000

        # Simulate response
        response_occurred = 1 if _import_system_random.random() > 0.2 else 0 # 80% chance of response
        first_response_ts = None
        all_responses_ts_list = []
        latency_calc = None

        if response_occurred:
            response_delay = _import_system_random.uniform(0.5, 3.0) # seconds
            time.sleep(response_delay)
            first_response_ts = time.time() * 1000
            all_responses_ts_list.append(first_response_ts)
            latency_calc = first_response_ts - sd_presentation_ts

            # Simulate potential multiple responses
            if _import_system_random.random() > 0.7:
                time.sleep(_import_system_random.uniform(0.2, 1.0))
                all_responses_ts_list.append(time.time() * 1000)


        time.sleep(_import_system_random.uniform(1.0, 2.0)) # Simulate rest of trial
        trial_end_ts = time.time() * 1000
        trial_duration_calc = trial_end_ts - trial_start_ts

        # Store in DB
        trial_id = database.add_trial(
            subject_id=subject_id,
            session_id=session_id,
            trial_number=i,
            task_type="simulated_task",
            target_skill="simulation",
            trial_start_time=trial_start_ts,
            sd_presentation_time=sd_presentation_ts,
            first_response_time=first_response_ts,
            all_response_times=all_responses_ts_list, # Pass the list
            trial_end_time=trial_end_ts,
            response_occurred=response_occurred,
            response_correct= 1 if response_occurred and _import_system_random.random() > 0.3 else 0,
            multiple_responses=len(all_responses_ts_list)
        )
        logger.debug(f"Trial {i} (ID: {trial_id}) data saved.")

        # Prepare data for R (matching Killeen_framework.R expected input)
        # Note: Killeen framework's prepare_response_data expects certain columns
        trial_dict_for_r = {
            "subject": subject_id,
            "session": session_id,
            "trial": i,
            "responses": len(all_responses_ts_list), # Count of responses in this trial
            "time_to_first_response": latency_calc / 1000 if latency_calc else None, # in seconds
            "trial_duration": trial_duration_calc / 1000 if trial_duration_calc else None, # in seconds
            "trial_start": trial_start_ts / 1000, # in seconds
            "trial_end": trial_end_ts / 1000, # in seconds
            # This is the tricky one: list of response timestamps *within this trial*, relative to trial start
            "response_times_in_trial": [(rt - trial_start_ts)/1000 for rt in all_responses_ts_list] if first_response_ts else [],
            # Add any other columns your R script's prepare_response_data might need
            # e.g. sd_presentation_time relative to trial_start
            "sd_presentation_time_relative": (sd_presentation_ts - trial_start_ts) / 1000 if sd_presentation_ts else None,
            "response_occurred_binary": response_occurred # Killeen script uses 'response'
        }
        trial_data_for_r.append(trial_dict_for_r)

    end_time_dt = datetime.datetime.now()
    end_time_iso = end_time_dt.isoformat()
    database.end_session(session_id, end_time_iso, num_trials)
    logger.info(f"--- DTT Session {session_id} Ended ---")
    return session_id, pd.DataFrame(trial_data_for_r)


def trigger_r_analysis(subject_id=None, session_id=None, db_path=None):
    """
    Triggers the R script for analysis.
    Passes subject_id and session_id as arguments to R.
    """
    if db_path is None:
        db_path = database.DATABASE_PATH

    logger.info(f"Triggering R analysis for Subject: {subject_id}, Session: {session_id} using DB: {db_path}")

    cmd = ["Rscript", R_SCRIPT_PATH]
    if subject_id:
        cmd.extend(["--subject", str(subject_id)])
    if session_id:
        cmd.extend(["--session", str(session_id)])
    cmd.extend(["--db_path", db_path])

    try:
        logger.debug(f"Running R command: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, check=True, encoding='utf-8')
        logger.info("R script executed successfully.")
        logger.info("R script stdout:")
        for line in result.stdout.splitlines():
            logger.info(line)
        if result.stderr:
            logger.warning("R script stderr:")
            for line in result.stderr.splitlines():
                logger.warning(line)

        # Potentially parse JSON output from R if R script prints results as JSON
        # For now, just log stdout

    except subprocess.CalledProcessError as e:
        logger.error(f"R script execution failed with exit code {e.returncode}.")
        logger.error(f"R script stdout: {e.stdout}")
        logger.error(f"R script stderr: {e.stderr}")
    except FileNotFoundError:
        logger.error(f"Rscript command not found. Is R installed and in PATH? R_SCRIPT_PATH: {R_SCRIPT_PATH}")
    except Exception as e:
        logger.error(f"An unexpected error occurred while running R script: {e}")

# --- Kivy App Class ---
class DTTApp(App):
    """Main DTT Application."""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.screen_manager = None
        self.main_screen = None
        self.trial_screen = None
        self.settings_screen = None
        self.current_task = None
        self.current_subject_id = None

    def build(self):
        """Build the application UI."""
        # Create screen manager
        self.screen_manager = ScreenManager()

        # Create screens
        self.main_screen = MainScreen()
        self.trial_screen = TrialScreen()
        self.settings_screen = SettingsScreen()

        # Set up navigation callbacks
        self.main_screen.on_start_session = self.start_session
        self.main_screen.on_settings = self.show_settings
        self.main_screen.on_run_analysis = self.run_analysis

        self.trial_screen.on_trial_complete = self.handle_trial_complete
        self.trial_screen.on_session_complete = self.handle_session_complete

        self.settings_screen.on_back_pressed = self.show_main_screen
        self.settings_screen.on_settings_changed = self.handle_settings_changed

        # Wrap screens in Screen objects
        main_screen_wrapper = Screen(name='main')
        main_screen_wrapper.add_widget(self.main_screen)

        trial_screen_wrapper = Screen(name='trial')
        trial_screen_wrapper.add_widget(self.trial_screen)

        settings_screen_wrapper = Screen(name='settings')
        settings_screen_wrapper.add_widget(self.settings_screen)

        # Add screens to manager
        self.screen_manager.add_widget(main_screen_wrapper)
        self.screen_manager.add_widget(trial_screen_wrapper)
        self.screen_manager.add_widget(settings_screen_wrapper)

        # Start on main screen
        self.screen_manager.current = 'main'

        logger.info("DTT Application built successfully")
        return self.screen_manager

    def start_session(self, subject_id: str):
        """Start a new DTT session."""
        try:
            self.current_subject_id = subject_id

            # Import task here to avoid circular imports
            from tasks.clothes_sorting import ClothesSortingTask

            # Get current settings
            settings = self.settings_screen.get_settings()
            positioning_mode = settings.get('positioning_mode', 'randomized_fixed')

            # Create and configure task
            self.current_task = ClothesSortingTask(positioning_mode=positioning_mode)

            # Validate assets before starting
            validation = self.current_task.validate_assets()
            if not validation['all_valid']:
                logger.warning("Some assets are missing, but continuing with available assets")

            self.current_task.start_session()

            # Set task in trial screen
            self.trial_screen.set_task(self.current_task)

            # Switch to trial screen
            self.screen_manager.current = 'trial'

            logger.info(f"Started session for subject {subject_id} with positioning mode: {positioning_mode}")

        except Exception as e:
            logger.error(f"Error starting session: {e}")
            self.main_screen.set_status(f"Error starting session: {e}")

    def show_settings(self):
        """Show settings screen."""
        self.screen_manager.current = 'settings'

    def show_main_screen(self):
        """Show main screen."""
        self.screen_manager.current = 'main'

    def run_analysis(self):
        """Run R analysis on collected data."""
        try:
            if self.current_subject_id:
                trigger_r_analysis(subject_id=self.current_subject_id)
                self.main_screen.set_status("Analysis completed successfully")
            else:
                trigger_r_analysis()
                self.main_screen.set_status("Analysis completed for all subjects")
        except Exception as e:
            logger.error(f"Error running analysis: {e}")
            self.main_screen.set_status(f"Analysis failed: {e}")

    def handle_trial_complete(self, trial_data):
        """Handle completion of a trial."""
        try:
            # Save trial data to database
            if self.current_subject_id and self.current_task:
                session_id = getattr(self.current_task, 'session_id', 'UNKNOWN')

                # Extract timing data
                timing_data = trial_data.timing_data or {}
                trial_start_time = timing_data.get('events', {}).get('trial_start', time.time() * 1000)
                sd_presentation_time = timing_data.get('events', {}).get('sd_presentation', trial_start_time)
                trial_end_time = timing_data.get('events', {}).get('trial_end', trial_start_time + 5000)
                first_response_time = timing_data.get('events', {}).get('first_response')

                # Extract response data
                response_data = trial_data.response_data or {}
                all_response_times = [first_response_time] if first_response_time else []

                # Determine correctness
                response_occurred = 1 if trial_data.result.value == 'correct' or trial_data.result.value == 'incorrect' else 0
                response_correct = 1 if trial_data.result.value == 'correct' else 0

                # Save to database
                database.add_trial(
                    subject_id=self.current_subject_id,
                    session_id=session_id,
                    trial_number=trial_data.trial_number,
                    task_type=trial_data.task_type,
                    target_skill=trial_data.target_skill,
                    trial_start_time=trial_start_time,
                    sd_presentation_time=sd_presentation_time,
                    trial_end_time=trial_end_time,
                    response_occurred=response_occurred,
                    first_response_time=first_response_time,
                    all_response_times=all_response_times,
                    response_correct=response_correct,
                    response_location=response_data.get('selected_bin', ''),
                    multiple_responses=len(all_response_times)
                )

                logger.info(f"Saved trial {trial_data.trial_number} to database")

        except Exception as e:
            logger.error(f"Error saving trial data: {e}")

    def handle_session_complete(self):
        """Handle completion of a session."""
        try:
            if self.current_task:
                # End session in database
                session_id = getattr(self.current_task, 'session_id', 'UNKNOWN')
                end_time = datetime.datetime.now().isoformat()
                total_trials = len(self.current_task.trial_data_list)

                database.end_session(session_id, end_time, total_trials)

                logger.info(f"Session {session_id} completed with {total_trials} trials")

            # Return to main screen
            self.screen_manager.current = 'main'
            self.main_screen.set_status("Session completed successfully")

        except Exception as e:
            logger.error(f"Error completing session: {e}")

    def handle_settings_changed(self, settings):
        """Handle settings changes."""
        logger.info(f"Settings updated: {settings}")
        # Apply settings to current task if active
        if self.current_task and 'difficulty_level' in settings:
            if hasattr(self.current_task, 'set_difficulty_level'):
                self.current_task.set_difficulty_level(settings['difficulty_level'])


def run_simulation_mode():
    """Run the application in simulation mode (no GUI)."""
    initialize_system()

    _import_system_random = __import__("random") # For simulation

    # Example: Add a subject if not exists
    test_subject_id = "SIM_SUB001"
    if not database.get_subject(test_subject_id):
        database.add_subject(test_subject_id, "Simulated Subject")

    # Run a simulated session
    sim_session_id, df_for_r = run_dtt_session(test_subject_id)

    # Trigger R analysis for the simulated session
    trigger_r_analysis(subject_id=test_subject_id, session_id=sim_session_id)

    logger.info("Simulation mode completed")


def run_gui_mode():
    """Run the application in GUI mode."""
    initialize_system()

    # Start Kivy Application
    logger.info("Starting DTT Kivy Application...")
    try:
        app = DTTApp()
        app.run()
    except Exception as e:
        logger.error(f"Error running Kivy application: {e}")
        logger.info("Falling back to simulation mode...")
        run_simulation_mode()


if __name__ == "__main__":
    import sys

    # Check command line arguments
    if len(sys.argv) > 1 and sys.argv[1] == "--simulate":
        run_simulation_mode()
    else:
        run_gui_mode()