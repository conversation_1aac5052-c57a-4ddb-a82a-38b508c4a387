# src/utils/audio.py
"""
Audio feedback functions for DTT system
"""

import os
import logging
from typing import Optional
from pathlib import Path

logger = logging.getLogger(__name__)

try:
    from plyer import audio
    PLYER_AVAILABLE = True
except ImportError:
    logger.warning("Plyer not available. Audio feedback will be limited.")
    PLYER_AVAILABLE = False

try:
    from kivy.core.audio import SoundLoader
    KIVY_AUDIO_AVAILABLE = True
except ImportError:
    logger.warning("Kivy audio not available.")
    KIVY_AUDIO_AVAILABLE = False


class AudioManager:
    """
    Manages audio feedback for DTT sessions.
    Provides correct/incorrect feedback sounds and other audio cues.
    """

    def __init__(self, sounds_dir: Optional[str] = None):
        """
        Initialize AudioManager.

        Args:
            sounds_dir: Directory containing sound files. Defaults to assets/sounds/
        """
        if sounds_dir is None:
            # Default to assets/sounds relative to project root
            project_root = Path(__file__).parent.parent.parent
            sounds_dir = project_root / "assets" / "sounds"

        self.sounds_dir = Path(sounds_dir)
        self.sounds_dir.mkdir(parents=True, exist_ok=True)

        # Sound file paths
        self.correct_sound_path = self.sounds_dir / "correct.wav"
        self.incorrect_sound_path = self.sounds_dir / "incorrect.wav"
        self.start_sound_path = self.sounds_dir / "start.wav"
        self.end_sound_path = self.sounds_dir / "end.wav"

        # Loaded sounds cache
        self.loaded_sounds = {}

        # Create default sounds if they don't exist
        self._create_default_sounds()

        logger.info(f"AudioManager initialized with sounds directory: {self.sounds_dir}")

    def _create_default_sounds(self):
        """Create default sound files if they don't exist."""
        # Check if actual sound files exist, if not create placeholders
        default_sounds = [
            self.correct_sound_path,
            self.incorrect_sound_path,
            self.start_sound_path,
            self.end_sound_path
        ]

        for sound_path in default_sounds:
            if not sound_path.exists():
                # Create empty placeholder file
                sound_path.touch()
                logger.debug(f"Created placeholder sound file: {sound_path}")
            else:
                logger.debug(f"Found existing sound file: {sound_path}")

    def load_sound(self, sound_path: Path) -> Optional[object]:
        """
        Load a sound file using available audio backend.

        Args:
            sound_path: Path to sound file

        Returns:
            Loaded sound object or None if loading failed
        """
        if not sound_path.exists():
            logger.warning(f"Sound file not found: {sound_path}")
            return None

        sound_key = str(sound_path)

        # Return cached sound if already loaded
        if sound_key in self.loaded_sounds:
            return self.loaded_sounds[sound_key]

        sound = None

        # Try Kivy SoundLoader first
        if KIVY_AUDIO_AVAILABLE:
            try:
                sound = SoundLoader.load(str(sound_path))
                if sound:
                    logger.debug(f"Loaded sound with Kivy: {sound_path}")
                else:
                    logger.warning(f"Kivy failed to load sound: {sound_path}")
            except Exception as e:
                logger.error(f"Error loading sound with Kivy: {e}")

        # Cache the sound (even if None)
        self.loaded_sounds[sound_key] = sound
        return sound

    def play_sound(self, sound_path: Path) -> bool:
        """
        Play a sound file.

        Args:
            sound_path: Path to sound file

        Returns:
            bool: True if sound played successfully, False otherwise
        """
        try:
            sound = self.load_sound(sound_path)
            if sound and hasattr(sound, 'play'):
                sound.play()
                logger.debug(f"Played sound: {sound_path}")
                return True
            else:
                logger.warning(f"Could not play sound: {sound_path}")
                return False
        except Exception as e:
            logger.error(f"Error playing sound {sound_path}: {e}")
            return False

    def play_correct_feedback(self) -> bool:
        """Play correct response feedback sound."""
        return self.play_sound(self.correct_sound_path)

    def play_incorrect_feedback(self) -> bool:
        """Play incorrect response feedback sound."""
        return self.play_sound(self.incorrect_sound_path)

    def play_session_start(self) -> bool:
        """Play session start sound."""
        return self.play_sound(self.start_sound_path)

    def play_session_end(self) -> bool:
        """Play session end sound."""
        return self.play_sound(self.end_sound_path)

    def play_feedback(self, correct: bool) -> bool:
        """
        Play appropriate feedback sound based on response correctness.

        Args:
            correct: True for correct response, False for incorrect

        Returns:
            bool: True if sound played successfully
        """
        if correct:
            return self.play_correct_feedback()
        else:
            return self.play_incorrect_feedback()

    def set_volume(self, volume: float):
        """
        Set volume for all sounds.

        Args:
            volume: Volume level (0.0 to 1.0)
        """
        # This would need to be implemented based on the audio backend
        logger.info(f"Volume set to {volume}")

    def stop_all_sounds(self):
        """Stop all currently playing sounds."""
        for sound in self.loaded_sounds.values():
            if sound and hasattr(sound, 'stop'):
                try:
                    sound.stop()
                except Exception as e:
                    logger.error(f"Error stopping sound: {e}")


# Global audio manager instance
_audio_manager = None


def get_audio_manager() -> AudioManager:
    """Get the global AudioManager instance."""
    global _audio_manager
    if _audio_manager is None:
        _audio_manager = AudioManager()
    return _audio_manager


# Convenience functions
def play_correct_feedback() -> bool:
    """Play correct response feedback."""
    return get_audio_manager().play_correct_feedback()


def play_incorrect_feedback() -> bool:
    """Play incorrect response feedback."""
    return get_audio_manager().play_incorrect_feedback()


def play_feedback(correct: bool) -> bool:
    """Play appropriate feedback sound."""
    return get_audio_manager().play_feedback(correct)
