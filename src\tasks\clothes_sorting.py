# src/tasks/clothes_sorting.py
"""
Clothes sorting DTT task implementation
"""

import random
from typing import Dict, Any, List, Tuple
import logging
from pathlib import Path
import os

from .base_task import BaseTask, TrialResult

logger = logging.getLogger(__name__)


class ClothesSortingTask(BaseTask):
    """
    DTT task for clothes sorting using laundry baskets.

    Presents shirt images and requires sorting into appropriate laundry baskets:
    - White shirts → White basket
    - Dark colored shirts → Black basket
    - Light colored shirts → Color basket
    """

    def __init__(self, positioning_mode="randomized_fixed"):
        super().__init__("clothes_sorting", "laundry_sorting")

        # Asset paths
        self.assets_dir = Path("assets")
        self.images_dir = self.assets_dir / "images"
        self.sounds_dir = self.assets_dir / "sounds"

        # Define shirt stimuli with their correct basket categories
        self.shirt_stimuli = {
            "Shirt_White.png": "white",
            "Shirt_Dark_Blue.png": "black",
            "Shirt_Grey.png": "black",
            "Shirt_Teal.png": "color",
            "Shirt_Yellow.png": "color"
        }

        # Define laundry baskets
        self.basket_info = {
            "white": {
                "image": "Laundry_Basket_White.png",
                "label": "White Clothes",
                "category": "white"
            },
            "black": {
                "image": "Laundry_Basket_Black.png",
                "label": "Dark Clothes",
                "category": "black"
            },
            "color": {
                "image": "Laundry_Basket_Color.png",
                "label": "Colored Clothes",
                "category": "color"
            }
        }

        # Positioning configuration
        self.positioning_mode = positioning_mode  # "fixed", "randomized_fixed", "random"
        self.session_basket_order = None  # For randomized_fixed mode

        # Screen layout configuration
        self.basket_y_position = 0.7  # Baskets in upper portion
        self.stimulus_y_position = 0.3  # Shirt below baskets
        self.basket_size = (0.2, 0.25)  # Width, height as screen fractions

        # Current trial state
        self.current_stimulus_widget = None
        self.current_basket_widgets = {}
        self.current_basket_positions = {}

        # Initialize basket positions for session
        self._initialize_basket_positions()

        logger.info(f"Initialized clothes sorting task with {positioning_mode} positioning")

    def _initialize_basket_positions(self):
        """Initialize basket positions based on positioning mode."""
        basket_categories = list(self.basket_info.keys())

        if self.positioning_mode == "fixed":
            # Always same order: White, Black, Color
            self.session_basket_order = ["white", "black", "color"]
        elif self.positioning_mode == "randomized_fixed":
            # Random order for session, stays consistent
            self.session_basket_order = basket_categories.copy()
            random.shuffle(self.session_basket_order)
        else:  # "random"
            # Will be randomized each trial
            self.session_basket_order = None

        self._update_basket_positions()
        logger.debug(f"Basket positioning initialized: {self.positioning_mode}")

    def _update_basket_positions(self):
        """Update basket positions based on current mode."""
        if self.positioning_mode == "random":
            # Randomize for this trial
            basket_order = list(self.basket_info.keys())
            random.shuffle(basket_order)
        else:
            # Use session order
            basket_order = self.session_basket_order

        # Calculate positions for 3 baskets horizontally
        x_positions = [0.2, 0.5, 0.8]  # Left, center, right

        self.current_basket_positions = {}
        for i, category in enumerate(basket_order):
            self.current_basket_positions[category] = {
                "position": (x_positions[i], self.basket_y_position),
                "size": self.basket_size,
                "image_path": str(self.images_dir / self.basket_info[category]["image"]),
                "label": self.basket_info[category]["label"]
            }

        logger.debug(f"Updated basket positions: {[cat for cat in basket_order]}")

    def generate_stimulus(self, trial_number: int) -> Dict[str, Any]:
        """
        Generate a shirt stimulus for sorting into laundry baskets.

        Args:
            trial_number: Current trial number

        Returns:
            dict: Stimulus data including shirt image, correct basket, and positions
        """
        # Update basket positions if using random mode
        if self.positioning_mode == "random":
            self._update_basket_positions()

        # Select random shirt
        shirt_filename = random.choice(list(self.shirt_stimuli.keys()))
        correct_basket = self.shirt_stimuli[shirt_filename]

        stimulus_data = {
            "shirt_image": shirt_filename,
            "shirt_image_path": str(self.images_dir / shirt_filename),
            "correct_basket": correct_basket,
            "stimulus_position": (0.5, self.stimulus_y_position),  # Center horizontally, below baskets
            "baskets": self.current_basket_positions.copy(),
            "trial_number": trial_number,
            "positioning_mode": self.positioning_mode
        }

        logger.debug(f"Generated stimulus: {shirt_filename} → {correct_basket} basket")
        return stimulus_data

    def present_stimulus(self, stimulus_data: Dict[str, Any]) -> float:
        """
        Present the shirt stimulus and laundry baskets on screen.

        Args:
            stimulus_data: Data describing the stimulus

        Returns:
            float: Timestamp when stimulus was presented
        """
        shirt_image = stimulus_data["shirt_image"]
        shirt_path = stimulus_data["shirt_image_path"]

        logger.info(f"Presenting stimulus: {shirt_image}")

        # Create shirt stimulus widget data
        self.current_stimulus_widget = {
            "type": "shirt_stimulus",
            "image_path": shirt_path,
            "image_filename": shirt_image,
            "position": stimulus_data["stimulus_position"],
            "size": (0.15, 0.2),  # Shirt size as screen fraction
            "visible": True,
            "draggable": True
        }

        # Create basket widgets data
        self.current_basket_widgets = {}
        for basket_category, basket_data in stimulus_data["baskets"].items():
            self.current_basket_widgets[basket_category] = {
                "type": "laundry_basket",
                "category": basket_category,
                "image_path": basket_data["image_path"],
                "position": basket_data["position"],
                "size": basket_data["size"],
                "label": basket_data["label"],
                "active": True
            }

        logger.debug(f"Created {len(self.current_basket_widgets)} basket widgets")

        # Return current timestamp
        from utils.timing import get_precise_timestamp
        return get_precise_timestamp()

    def evaluate_response(self, response_data: Dict[str, Any],
                         stimulus_data: Dict[str, Any]) -> TrialResult:
        """
        Evaluate if the participant sorted the shirt into the correct basket.

        Args:
            response_data: Data about which basket was selected
            stimulus_data: Data about the presented stimulus

        Returns:
            TrialResult: CORRECT if sorted to right basket, INCORRECT otherwise
        """
        if "selected_basket" not in response_data:
            return TrialResult.NO_RESPONSE

        selected_basket = response_data["selected_basket"]
        correct_basket = stimulus_data["correct_basket"]

        if selected_basket == correct_basket:
            logger.debug(f"Correct response: {selected_basket} basket")
            return TrialResult.CORRECT
        else:
            logger.debug(f"Incorrect response: {selected_basket} basket (correct: {correct_basket})")
            return TrialResult.INCORRECT

    def cleanup_trial(self):
        """Clean up after trial completion."""
        # Remove stimulus and basket widgets
        self.current_stimulus_widget = None
        self.current_basket_widgets = {}
        logger.debug("Cleaned up trial widgets")

    def check_touch_in_basket(self, touch_pos: Tuple[float, float]) -> str:
        """
        Check which basket (if any) was touched.

        Args:
            touch_pos: Touch position as (x, y) normalized coordinates (0-1)

        Returns:
            str: Category of touched basket, or empty string if no basket touched
        """
        x, y = touch_pos

        for basket_category, basket_data in self.current_basket_positions.items():
            basket_x, basket_y = basket_data["position"]
            basket_w, basket_h = basket_data["size"]

            # Check if touch is within basket bounds
            if (basket_x - basket_w/2 <= x <= basket_x + basket_w/2 and
                basket_y - basket_h/2 <= y <= basket_y + basket_h/2):
                return basket_category

        return ""

    def handle_touch(self, touch_pos: Tuple[float, float]) -> Dict[str, Any]:
        """
        Handle a touch/click event during the trial.

        Args:
            touch_pos: Touch position as (x, y) normalized coordinates

        Returns:
            dict: Response data including selected basket
        """
        selected_basket = self.check_touch_in_basket(touch_pos)

        response_data = {
            "touch_position": touch_pos,
            "selected_basket": selected_basket,
            "response_type": "touch"
        }

        logger.debug(f"Touch at {touch_pos}, selected basket: {selected_basket}")
        return response_data

    def handle_drag_drop(self, start_pos: Tuple[float, float],
                        end_pos: Tuple[float, float]) -> Dict[str, Any]:
        """
        Handle a drag and drop event during the trial.

        Args:
            start_pos: Starting position of drag (x, y) normalized coordinates
            end_pos: Ending position of drag (x, y) normalized coordinates

        Returns:
            dict: Response data including selected basket
        """
        selected_basket = self.check_touch_in_basket(end_pos)

        response_data = {
            "start_position": start_pos,
            "end_position": end_pos,
            "selected_basket": selected_basket,
            "response_type": "drag_drop"
        }

        logger.debug(f"Drag from {start_pos} to {end_pos}, selected basket: {selected_basket}")
        return response_data

    def get_task_instructions(self) -> str:
        """Get instructions for this task."""
        return ("Sort the shirts into the correct laundry baskets:\n"
                "• White shirts → White basket\n"
                "• Dark colored shirts → Black basket\n"
                "• Light colored shirts → Color basket\n"
                "Touch and drag the shirt to the correct basket.")

    def get_available_shirts(self) -> List[str]:
        """Get list of available shirt stimuli."""
        return list(self.shirt_stimuli.keys())

    def get_basket_categories(self) -> List[str]:
        """Get list of basket categories."""
        return list(self.basket_info.keys())

    def set_positioning_mode(self, mode: str):
        """
        Set basket positioning mode.

        Args:
            mode: Positioning mode ("fixed", "randomized_fixed", "random")
        """
        if mode in ["fixed", "randomized_fixed", "random"]:
            self.positioning_mode = mode
            self._initialize_basket_positions()
            logger.info(f"Set positioning mode to: {mode}")
        else:
            logger.warning(f"Invalid positioning mode: {mode}")

    def get_positioning_mode(self) -> str:
        """Get current positioning mode."""
        return self.positioning_mode

    def get_current_basket_order(self) -> List[str]:
        """Get current basket order (left to right)."""
        if self.positioning_mode == "random":
            # Return current trial order
            positions = [(cat, data["position"][0]) for cat, data in self.current_basket_positions.items()]
            positions.sort(key=lambda x: x[1])  # Sort by x position
            return [cat for cat, _ in positions]
        else:
            return self.session_basket_order.copy()

    def get_asset_paths(self) -> Dict[str, str]:
        """Get paths to all required assets."""
        assets = {
            "shirts": {},
            "baskets": {},
            "sounds": {}
        }

        # Shirt assets
        for shirt_file in self.shirt_stimuli.keys():
            assets["shirts"][shirt_file] = str(self.images_dir / shirt_file)

        # Basket assets
        for category, info in self.basket_info.items():
            assets["baskets"][category] = str(self.images_dir / info["image"])

        # Sound assets
        assets["sounds"]["correct"] = str(self.sounds_dir / "correct.wav")
        assets["sounds"]["incorrect"] = str(self.sounds_dir / "incorrect.wav")

        return assets

    def validate_assets(self) -> Dict[str, bool]:
        """
        Validate that all required assets exist.

        Returns:
            dict: Asset validation results
        """
        validation = {
            "shirts": {},
            "baskets": {},
            "sounds": {},
            "all_valid": True
        }

        # Check shirt images
        for shirt_file in self.shirt_stimuli.keys():
            path = self.images_dir / shirt_file
            exists = path.exists()
            validation["shirts"][shirt_file] = exists
            if not exists:
                validation["all_valid"] = False
                logger.warning(f"Missing shirt asset: {path}")

        # Check basket images
        for category, info in self.basket_info.items():
            path = self.images_dir / info["image"]
            exists = path.exists()
            validation["baskets"][category] = exists
            if not exists:
                validation["all_valid"] = False
                logger.warning(f"Missing basket asset: {path}")

        # Check sound files
        for sound_name in ["correct.wav", "incorrect.wav"]:
            path = self.sounds_dir / sound_name
            exists = path.exists()
            validation["sounds"][sound_name] = exists
            if not exists:
                validation["all_valid"] = False
                logger.warning(f"Missing sound asset: {path}")

        if validation["all_valid"]:
            logger.info("All assets validated successfully")
        else:
            logger.warning("Some assets are missing")

        return validation
